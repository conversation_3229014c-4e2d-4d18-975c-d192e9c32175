<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Queue Infrastructure Configuration
    |--------------------------------------------------------------------------
    |
    | Phase 5C: Background Processing Enhancement konfigürasyonu
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Priority Management
    |--------------------------------------------------------------------------
    */
    'priority_management' => [
        'enabled' => env('QUEUE_PRIORITY_MANAGEMENT_ENABLED', true),
        
        'default_queue' => env('QUEUE_DEFAULT', 'default'),
        
        'queue_mappings' => [
            'critical' => [
                'payment' => 'payments-critical',
                'order' => 'orders-critical',
                'notification' => 'notifications-critical',
                'security' => 'security-critical',
                'default' => 'critical',
            ],
            'high' => [
                'payment' => 'payments-high',
                'order' => 'orders-high',
                'notification' => 'notifications-high',
                'cache' => 'cache-high',
                'default' => 'high',
            ],
            'medium' => [
                'payment' => 'payments',
                'order' => 'orders',
                'notification' => 'notifications',
                'cache' => 'cache-medium',
                'email' => 'emails',
                'default' => 'default',
            ],
            'low' => [
                'analytics' => 'analytics-low',
                'cleanup' => 'cleanup-low',
                'cache' => 'cache-low',
                'default' => 'low',
            ],
        ],

        'priority_thresholds' => [
            'critical' => [1, 2],
            'high' => [3, 4, 5],
            'medium' => [6, 7],
            'low' => [8, 9, 10],
        ],

        'capacity_thresholds' => [
            'critical' => 100,
            'high' => 500,
            'default' => 1000,
            'low' => 2000,
            'payments-critical' => 50,
            'orders-critical' => 100,
            'notifications-critical' => 200,
        ],

        'alternative_queues' => [
            'critical' => ['high', 'default'],
            'high' => ['default', 'medium'],
            'default' => ['medium', 'low'],
            'payments-critical' => ['payments-high', 'payments'],
            'orders-critical' => ['orders-high', 'orders'],
        ],

        'escalation_rules' => [
            'default' => [
                [
                    'threshold' => 300, // 5 dakika
                    'priority_boost' => 1,
                    'action' => 'log',
                ],
                [
                    'threshold' => 900, // 15 dakika
                    'priority_boost' => 2,
                    'action' => 'notify_admin',
                ],
                [
                    'threshold' => 1800, // 30 dakika
                    'priority_boost' => 3,
                    'action' => 'move_to_priority_queue',
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Batch Processing
    |--------------------------------------------------------------------------
    */
    'batch_processing' => [
        'enabled' => env('QUEUE_BATCH_PROCESSING_ENABLED', true),
        
        'default_chunk_size' => env('QUEUE_DEFAULT_CHUNK_SIZE', 100),
        'max_parallel_jobs' => env('QUEUE_MAX_PARALLEL_JOBS', 5),
        'batch_timeout' => env('QUEUE_BATCH_TIMEOUT', 3600), // 1 hour
        
        'failure_handling' => [
            'strategy' => env('QUEUE_BATCH_FAILURE_STRATEGY', 'continue'), // continue, stop, retry
            'max_retries' => env('QUEUE_BATCH_MAX_RETRIES', 3),
            'retry_delay' => env('QUEUE_BATCH_RETRY_DELAY', 60), // seconds
        ],

        'progress_tracking' => [
            'enabled' => env('QUEUE_BATCH_PROGRESS_TRACKING', true),
            'update_interval' => env('QUEUE_BATCH_PROGRESS_UPDATE_INTERVAL', 10), // seconds
            'cache_ttl' => env('QUEUE_BATCH_PROGRESS_CACHE_TTL', 3600), // seconds
        ],

        'resource_limits' => [
            'max_memory_per_job' => env('QUEUE_BATCH_MAX_MEMORY_PER_JOB', '256M'),
            'max_execution_time' => env('QUEUE_BATCH_MAX_EXECUTION_TIME', 300), // seconds
            'max_concurrent_batches' => env('QUEUE_BATCH_MAX_CONCURRENT', 10),
        ],

        'cleanup' => [
            'auto_cleanup_enabled' => env('QUEUE_BATCH_AUTO_CLEANUP', true),
            'cleanup_after_days' => env('QUEUE_BATCH_CLEANUP_AFTER_DAYS', 7),
            'keep_failed_batches_days' => env('QUEUE_BATCH_KEEP_FAILED_DAYS', 30),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Health Monitoring
    |--------------------------------------------------------------------------
    */
    'health_monitoring' => [
        'enabled' => env('QUEUE_HEALTH_MONITORING_ENABLED', true),
        
        'check_interval' => env('QUEUE_HEALTH_CHECK_INTERVAL', 60), // seconds
        'alert_cooldown' => env('QUEUE_HEALTH_ALERT_COOLDOWN', 300), // seconds
        
        'monitored_queues' => [
            'critical',
            'high',
            'default',
            'low',
            'payments',
            'orders',
            'emails',
            'notifications',
            'cache-high',
            'cache-medium',
            'cache-low',
        ],

        'thresholds' => [
            'queue_size' => [
                'critical' => env('QUEUE_THRESHOLD_CRITICAL', 100),
                'high' => env('QUEUE_THRESHOLD_HIGH', 500),
                'default' => env('QUEUE_THRESHOLD_DEFAULT', 1000),
                'low' => env('QUEUE_THRESHOLD_LOW', 2000),
            ],
            
            'failed_jobs' => [
                'total' => env('QUEUE_FAILED_JOBS_THRESHOLD', 100),
                'recent' => env('QUEUE_FAILED_JOBS_RECENT_THRESHOLD', 10), // per hour
            ],
            
            'processing_rate' => [
                'min' => env('QUEUE_MIN_PROCESSING_RATE', 10), // jobs per minute
                'max' => env('QUEUE_MAX_PROCESSING_RATE', 1000), // jobs per minute
            ],
            
            'response_time' => [
                'warning' => env('QUEUE_RESPONSE_TIME_WARNING', 5000), // ms
                'critical' => env('QUEUE_RESPONSE_TIME_CRITICAL', 10000), // ms
            ],
            
            'memory_usage' => [
                'warning' => env('QUEUE_MEMORY_WARNING_PERCENT', 80),
                'critical' => env('QUEUE_MEMORY_CRITICAL_PERCENT', 90),
            ],
            
            'min_workers' => env('QUEUE_MIN_WORKERS', 1),
            'dead_letter_queue' => env('QUEUE_DLQ_THRESHOLD', 50),
        ],

        'alert_channels' => [
            'log' => [
                'enabled' => env('QUEUE_ALERT_LOG_ENABLED', true),
                'level' => env('QUEUE_ALERT_LOG_LEVEL', 'error'),
            ],
            'email' => [
                'enabled' => env('QUEUE_ALERT_EMAIL_ENABLED', false),
                'recipients' => explode(',', env('QUEUE_ALERT_EMAIL_RECIPIENTS', '')),
            ],
            'slack' => [
                'enabled' => env('QUEUE_ALERT_SLACK_ENABLED', false),
                'webhook_url' => env('QUEUE_ALERT_SLACK_WEBHOOK'),
                'channel' => env('QUEUE_ALERT_SLACK_CHANNEL', '#alerts'),
            ],
            'webhook' => [
                'enabled' => env('QUEUE_ALERT_WEBHOOK_ENABLED', false),
                'url' => env('QUEUE_ALERT_WEBHOOK_URL'),
                'method' => env('QUEUE_ALERT_WEBHOOK_METHOD', 'POST'),
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Analytics
    |--------------------------------------------------------------------------
    */
    'performance_analytics' => [
        'enabled' => env('QUEUE_PERFORMANCE_ANALYTICS_ENABLED', true),
        
        'metrics_collection' => [
            'job_execution_time' => true,
            'queue_wait_time' => true,
            'memory_usage' => true,
            'throughput' => true,
            'error_rates' => true,
        ],

        'retention' => [
            'raw_metrics_days' => env('QUEUE_METRICS_RAW_RETENTION_DAYS', 7),
            'aggregated_metrics_days' => env('QUEUE_METRICS_AGGREGATED_RETENTION_DAYS', 90),
        ],

        'aggregation_intervals' => [
            'minute' => true,
            'hour' => true,
            'day' => true,
        ],

        'reporting' => [
            'daily_report_enabled' => env('QUEUE_DAILY_REPORT_ENABLED', true),
            'weekly_report_enabled' => env('QUEUE_WEEKLY_REPORT_ENABLED', true),
            'report_recipients' => explode(',', env('QUEUE_REPORT_RECIPIENTS', '')),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Dead Letter Queue
    |--------------------------------------------------------------------------
    */
    'dead_letter_queue' => [
        'enabled' => env('QUEUE_DLQ_ENABLED', true),
        'queue_name' => env('QUEUE_DLQ_NAME', 'dead_letter'),
        'max_retries' => env('QUEUE_DLQ_MAX_RETRIES', 5),
        'retry_delay' => env('QUEUE_DLQ_RETRY_DELAY', 300), // seconds
        'auto_retry_enabled' => env('QUEUE_DLQ_AUTO_RETRY', false),
        'retention_days' => env('QUEUE_DLQ_RETENTION_DAYS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Dynamic Scaling
    |--------------------------------------------------------------------------
    */
    'dynamic_scaling' => [
        'enabled' => env('QUEUE_DYNAMIC_SCALING_ENABLED', false),
        
        'scaling_rules' => [
            'scale_up' => [
                'queue_size_threshold' => 100,
                'wait_time_threshold' => 300, // seconds
                'max_workers' => 20,
            ],
            'scale_down' => [
                'idle_time_threshold' => 600, // seconds
                'min_workers' => 1,
            ],
        ],

        'scaling_cooldown' => env('QUEUE_SCALING_COOLDOWN', 300), // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Job Retry Strategies
    |--------------------------------------------------------------------------
    */
    'retry_strategies' => [
        'exponential_backoff' => [
            'base_delay' => 60, // seconds
            'max_delay' => 3600, // seconds
            'multiplier' => 2,
        ],
        'linear_backoff' => [
            'base_delay' => 60, // seconds
            'increment' => 60, // seconds
        ],
        'fixed_delay' => [
            'delay' => 300, // seconds
        ],
    ],
];
