<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Elasticsearch Connection
    |--------------------------------------------------------------------------
    |
    | This option controls the default Elasticsearch connection that gets used
    | while using the Elasticsearch client. This connection is used when
    | syncing all models to the search service.
    |
    */

    'default' => env('ELASTICSEARCH_CONNECTION', 'default'),

    /*
    |--------------------------------------------------------------------------
    | Elasticsearch Connections
    |--------------------------------------------------------------------------
    |
    | Here you may configure your Elasticsearch connections. Multiple
    | connections are supported for different environments or use cases.
    |
    */

    'connections' => [
        'default' => [
            'hosts' => [
                env('ELASTICSEARCH_HOST', 'localhost:9200'),
            ],
            'username' => env('ELASTICSEARCH_USERNAME'),
            'password' => env('ELASTICSEARCH_PASSWORD'),
            'cloud_id' => env('ELASTICSEARCH_CLOUD_ID'),
            'api_key' => env('ELASTICSEARCH_API_KEY'),
            'ssl_verification' => env('ELASTICSEARCH_SSL_VERIFICATION', true),
            'retries' => env('ELASTICSEARCH_RETRIES', 2),
            'timeout' => env('ELASTICSEARCH_TIMEOUT', 30),
            'connection_pool' => env('ELASTICSEARCH_CONNECTION_POOL', 'StaticNoPingConnectionPool'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Index Settings
    |--------------------------------------------------------------------------
    |
    | Here you may configure your Elasticsearch index settings for different
    | models and use cases.
    |
    */

    'indices' => [
        'products' => [
            'name' => env('ELASTICSEARCH_PRODUCTS_INDEX', 'products'),
            'settings' => [
                'number_of_shards' => env('ELASTICSEARCH_PRODUCTS_SHARDS', 1),
                'number_of_replicas' => env('ELASTICSEARCH_PRODUCTS_REPLICAS', 1),
                'analysis' => [
                    'analyzer' => [
                        'turkish_analyzer' => [
                            'type' => 'custom',
                            'tokenizer' => 'standard',
                            'filter' => [
                                'lowercase',
                                'turkish_stop',
                                'turkish_stemmer',
                                'asciifolding',
                            ],
                        ],
                        'autocomplete_analyzer' => [
                            'type' => 'custom',
                            'tokenizer' => 'autocomplete_tokenizer',
                            'filter' => [
                                'lowercase',
                                'asciifolding',
                            ],
                        ],
                        'search_analyzer' => [
                            'type' => 'custom',
                            'tokenizer' => 'standard',
                            'filter' => [
                                'lowercase',
                                'asciifolding',
                            ],
                        ],
                    ],
                    'tokenizer' => [
                        'autocomplete_tokenizer' => [
                            'type' => 'edge_ngram',
                            'min_gram' => 2,
                            'max_gram' => 20,
                            'token_chars' => [
                                'letter',
                                'digit',
                            ],
                        ],
                    ],
                    'filter' => [
                        'turkish_stop' => [
                            'type' => 'stop',
                            'stopwords' => '_turkish_',
                        ],
                        'turkish_stemmer' => [
                            'type' => 'stemmer',
                            'language' => 'turkish',
                        ],
                    ],
                ],
            ],
            'mappings' => [
                'properties' => [
                    'id' => ['type' => 'long'],
                    'name' => [
                        'type' => 'text',
                        'analyzer' => 'turkish_analyzer',
                        'fields' => [
                            'autocomplete' => [
                                'type' => 'text',
                                'analyzer' => 'autocomplete_analyzer',
                                'search_analyzer' => 'search_analyzer',
                            ],
                            'keyword' => [
                                'type' => 'keyword',
                            ],
                        ],
                    ],
                    'description' => [
                        'type' => 'text',
                        'analyzer' => 'turkish_analyzer',
                    ],
                    'sku' => [
                        'type' => 'keyword',
                    ],
                    'price' => [
                        'type' => 'double',
                    ],
                    'category_id' => [
                        'type' => 'long',
                    ],
                    'category_name' => [
                        'type' => 'text',
                        'analyzer' => 'turkish_analyzer',
                        'fields' => [
                            'keyword' => [
                                'type' => 'keyword',
                            ],
                        ],
                    ],
                    'brand' => [
                        'type' => 'text',
                        'analyzer' => 'turkish_analyzer',
                        'fields' => [
                            'keyword' => [
                                'type' => 'keyword',
                            ],
                        ],
                    ],
                    'tags' => [
                        'type' => 'text',
                        'analyzer' => 'turkish_analyzer',
                    ],
                    'attributes' => [
                        'type' => 'nested',
                        'properties' => [
                            'name' => ['type' => 'keyword'],
                            'value' => ['type' => 'text', 'analyzer' => 'turkish_analyzer'],
                        ],
                    ],
                    'status' => [
                        'type' => 'keyword',
                    ],
                    'stock_quantity' => [
                        'type' => 'integer',
                    ],
                    'created_at' => [
                        'type' => 'date',
                    ],
                    'updated_at' => [
                        'type' => 'date',
                    ],
                    'popularity_score' => [
                        'type' => 'float',
                    ],
                    'sales_count' => [
                        'type' => 'integer',
                    ],
                    'view_count' => [
                        'type' => 'integer',
                    ],
                ],
            ],
        ],

        'analytics' => [
            'name' => env('ELASTICSEARCH_ANALYTICS_INDEX', 'analytics'),
            'settings' => [
                'number_of_shards' => env('ELASTICSEARCH_ANALYTICS_SHARDS', 1),
                'number_of_replicas' => env('ELASTICSEARCH_ANALYTICS_REPLICAS', 0),
                'refresh_interval' => '5s',
            ],
            'mappings' => [
                'properties' => [
                    'event_type' => ['type' => 'keyword'],
                    'user_id' => ['type' => 'long'],
                    'session_id' => ['type' => 'keyword'],
                    'product_id' => ['type' => 'long'],
                    'category_id' => ['type' => 'long'],
                    'search_query' => ['type' => 'text'],
                    'timestamp' => ['type' => 'date'],
                    'metadata' => ['type' => 'object'],
                    'ip_address' => ['type' => 'ip'],
                    'user_agent' => ['type' => 'text'],
                    'revenue' => ['type' => 'double'],
                    'conversion' => ['type' => 'boolean'],
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Search Settings
    |--------------------------------------------------------------------------
    |
    | Global search configuration settings.
    |
    */

    'search' => [
        'default_size' => env('ELASTICSEARCH_DEFAULT_SIZE', 20),
        'max_size' => env('ELASTICSEARCH_MAX_SIZE', 1000),
        'timeout' => env('ELASTICSEARCH_SEARCH_TIMEOUT', '30s'),
        'track_total_hits' => env('ELASTICSEARCH_TRACK_TOTAL_HITS', true),
        'highlight' => [
            'enabled' => env('ELASTICSEARCH_HIGHLIGHT_ENABLED', true),
            'pre_tags' => ['<mark>'],
            'post_tags' => ['</mark>'],
            'fragment_size' => 150,
            'number_of_fragments' => 3,
        ],
        'boost' => [
            'name' => 3.0,
            'name.autocomplete' => 2.0,
            'description' => 1.0,
            'sku' => 2.5,
            'tags' => 1.5,
            'brand' => 2.0,
            'category_name' => 1.8,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Analytics Settings
    |--------------------------------------------------------------------------
    |
    | Analytics and monitoring configuration.
    |
    */

    'analytics' => [
        'enabled' => env('ELASTICSEARCH_ANALYTICS_ENABLED', true),
        'batch_size' => env('ELASTICSEARCH_ANALYTICS_BATCH_SIZE', 100),
        'flush_interval' => env('ELASTICSEARCH_ANALYTICS_FLUSH_INTERVAL', 30),
        'retention_days' => env('ELASTICSEARCH_ANALYTICS_RETENTION_DAYS', 90),
        'real_time' => env('ELASTICSEARCH_ANALYTICS_REAL_TIME', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Performance optimization settings.
    |
    */

    'performance' => [
        'cache_enabled' => env('ELASTICSEARCH_CACHE_ENABLED', true),
        'cache_ttl' => env('ELASTICSEARCH_CACHE_TTL', 300), // 5 minutes
        'bulk_size' => env('ELASTICSEARCH_BULK_SIZE', 1000),
        'scroll_size' => env('ELASTICSEARCH_SCROLL_SIZE', 1000),
        'scroll_timeout' => env('ELASTICSEARCH_SCROLL_TIMEOUT', '5m'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Settings
    |--------------------------------------------------------------------------
    |
    | Elasticsearch logging configuration.
    |
    */

    'logging' => [
        'enabled' => env('ELASTICSEARCH_LOGGING_ENABLED', false),
        'level' => env('ELASTICSEARCH_LOG_LEVEL', 'info'),
        'channel' => env('ELASTICSEARCH_LOG_CHANNEL', 'elasticsearch'),
    ],

];
