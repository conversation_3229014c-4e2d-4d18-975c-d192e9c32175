<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Stock Locations - Depo/lokasyon yönetimi
        Schema::create('stock_locations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->string('type')->default('warehouse'); // warehouse, store, virtual
            $table->text('description')->nullable();
            $table->json('address')->nullable(); // Adres bilgileri
            $table->json('contact_info')->nullable(); // İletişim bilgileri
            $table->json('settings')->nullable(); // Lokasyon ayarları
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->integer('priority')->default(0);
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['is_active', 'type']);
            $table->index('is_default');
            $table->index('priority');
        });

        // Stocks - Ana stok tablosu
        Schema::create('stocks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_variant_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('location_id')->constrained('stock_locations')->onDelete('cascade');
            
            // Stok seviyeleri
            $table->integer('available_quantity')->default(0);
            $table->integer('reserved_quantity')->default(0);
            $table->integer('total_quantity')->default(0);
            $table->integer('committed_quantity')->default(0); // Sipariş edilmiş
            $table->integer('incoming_quantity')->default(0); // Gelen stok
            
            // Threshold'lar
            $table->integer('low_stock_threshold')->default(0);
            $table->integer('reorder_level')->default(0);
            $table->integer('max_stock_level')->nullable();
            
            // Stok yönetimi ayarları
            $table->boolean('track_inventory')->default(true);
            $table->boolean('allow_backorder')->default(false);
            $table->boolean('is_active')->default(true);
            
            // Metadata
            $table->json('reservations')->nullable(); // Aktif rezervasyonlar
            $table->json('metadata')->nullable(); // Ek bilgiler
            $table->timestamp('last_movement_at')->nullable();
            $table->timestamp('last_count_at')->nullable(); // Son sayım tarihi
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->unique(['product_id', 'product_variant_id', 'location_id'], 'unique_product_location');
            $table->index(['product_id', 'location_id']);
            $table->index(['available_quantity', 'is_active']);
            $table->index(['low_stock_threshold', 'available_quantity']);
            $table->index(['reorder_level', 'available_quantity']);
            $table->index('track_inventory');
            $table->index('last_movement_at');
        });

        // Stock Movements - Stok hareketleri
        Schema::create('stock_movements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('stock_id')->constrained()->onDelete('cascade');
            $table->string('type'); // in, out, adjustment, transfer, reservation, release
            $table->string('reason'); // purchase, sale, adjustment, transfer, etc.
            $table->string('reference_type')->nullable(); // order, transfer, adjustment
            $table->unsignedBigInteger('reference_id')->nullable();
            
            // Hareket detayları
            $table->integer('quantity'); // Pozitif veya negatif
            $table->integer('previous_quantity');
            $table->integer('new_quantity');
            $table->decimal('unit_cost', 10, 4)->nullable();
            $table->decimal('total_cost', 12, 4)->nullable();
            
            // Metadata
            $table->json('metadata')->nullable();
            $table->string('batch_number')->nullable();
            $table->date('expiry_date')->nullable();
            $table->string('serial_number')->nullable();
            
            // Tracking
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamp('movement_date');
            $table->timestamps();

            // Indexes
            $table->index(['stock_id', 'movement_date']);
            $table->index(['type', 'movement_date']);
            $table->index(['reference_type', 'reference_id']);
            $table->index('movement_date');
            $table->index('created_by');

            // Foreign keys
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
        });

        // Stock Reservations - Stok rezervasyonları
        Schema::create('stock_reservations', function (Blueprint $table) {
            $table->id();
            $table->string('reservation_id')->unique(); // UUID benzeri
            $table->foreignId('stock_id')->constrained()->onDelete('cascade');
            $table->string('reference_type'); // order, cart, quote
            $table->unsignedBigInteger('reference_id');
            
            // Rezervasyon detayları
            $table->integer('quantity');
            $table->string('status')->default('active'); // active, expired, released, fulfilled
            $table->string('reason')->default('order');
            $table->json('metadata')->nullable();
            
            // Timing
            $table->timestamp('reserved_at');
            $table->timestamp('expires_at')->nullable();
            $table->timestamp('released_at')->nullable();
            $table->timestamp('fulfilled_at')->nullable();
            
            // Tracking
            $table->unsignedBigInteger('created_by')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['stock_id', 'status']);
            $table->index(['reference_type', 'reference_id']);
            $table->index(['status', 'expires_at']);
            $table->index('reservation_id');
            $table->index('reserved_at');

            // Foreign keys
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
        });

        // Stock Alerts - Stok uyarıları
        Schema::create('stock_alerts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('stock_id')->constrained()->onDelete('cascade');
            $table->string('type'); // low_stock, out_of_stock, reorder_needed, overstock
            $table->string('severity')->default('medium'); // low, medium, high, critical
            $table->string('status')->default('active'); // active, acknowledged, resolved
            
            // Alert detayları
            $table->string('title');
            $table->text('message');
            $table->json('data')->nullable(); // Alert ile ilgili ek data
            $table->integer('current_quantity');
            $table->integer('threshold_quantity')->nullable();
            
            // Timing
            $table->timestamp('triggered_at');
            $table->timestamp('acknowledged_at')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->unsignedBigInteger('acknowledged_by')->nullable();
            $table->unsignedBigInteger('resolved_by')->nullable();
            
            $table->timestamps();

            // Indexes
            $table->index(['stock_id', 'status']);
            $table->index(['type', 'severity']);
            $table->index(['status', 'triggered_at']);
            $table->index('triggered_at');

            // Foreign keys
            $table->foreign('acknowledged_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('resolved_by')->references('id')->on('users')->onDelete('set null');
        });

        // Stock Adjustments - Stok düzeltmeleri
        Schema::create('stock_adjustments', function (Blueprint $table) {
            $table->id();
            $table->string('adjustment_number')->unique(); // ADJ-2024-001
            $table->foreignId('location_id')->constrained('stock_locations')->onDelete('cascade');
            $table->string('type'); // count, correction, write_off, write_on
            $table->string('status')->default('pending'); // pending, approved, rejected, completed
            $table->string('reason');
            $table->text('notes')->nullable();
            
            // Totals
            $table->integer('total_items')->default(0);
            $table->decimal('total_value_change', 12, 4)->default(0);
            
            // Approval workflow
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->text('approval_notes')->nullable();
            
            $table->timestamps();

            // Indexes
            $table->index(['location_id', 'status']);
            $table->index(['type', 'status']);
            $table->index('adjustment_number');
            $table->index('created_by');

            // Foreign keys
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
        });

        // Stock Adjustment Items - Düzeltme kalemleri
        Schema::create('stock_adjustment_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('adjustment_id')->constrained('stock_adjustments')->onDelete('cascade');
            $table->foreignId('stock_id')->constrained()->onDelete('cascade');
            
            // Quantities
            $table->integer('system_quantity'); // Sistemdeki miktar
            $table->integer('actual_quantity'); // Gerçek miktar
            $table->integer('difference'); // Fark
            $table->decimal('unit_cost', 10, 4)->nullable();
            $table->decimal('value_change', 12, 4)->nullable();
            
            // Metadata
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['adjustment_id', 'stock_id']);
            $table->index('difference');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_adjustment_items');
        Schema::dropIfExists('stock_adjustments');
        Schema::dropIfExists('stock_alerts');
        Schema::dropIfExists('stock_reservations');
        Schema::dropIfExists('stock_movements');
        Schema::dropIfExists('stocks');
        Schema::dropIfExists('stock_locations');
    }
};
