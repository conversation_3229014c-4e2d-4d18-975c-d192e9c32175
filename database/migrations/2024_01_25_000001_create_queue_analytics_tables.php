<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Queue Events - Job execution events for analytics
        Schema::create('queue_events', function (Blueprint $table) {
            $table->id();
            $table->string('event_type', 50)->index(); // job_started, job_completed, job_failed, job_retry
            $table->string('queue', 100)->nullable()->index();
            $table->string('job_class', 255)->nullable()->index();
            $table->string('job_id', 100)->nullable()->index();
            $table->string('status', 50)->nullable()->index(); // pending, processing, completed, failed, retry
            
            // Performance metrics
            $table->decimal('execution_time', 10, 3)->nullable(); // seconds
            $table->bigInteger('memory_usage')->nullable(); // bytes
            $table->integer('cpu_usage')->nullable(); // percentage
            $table->decimal('queue_wait_time', 10, 3)->nullable(); // seconds
            
            // Error information
            $table->string('error_type', 255)->nullable()->index();
            $table->text('error_message')->nullable();
            $table->text('stack_trace')->nullable();
            
            // Job metadata
            $table->json('metadata')->nullable(); // Additional job-specific data
            $table->json('payload_size')->nullable(); // Job payload information
            
            // Context information
            $table->string('worker_id', 100)->nullable()->index();
            $table->string('connection', 50)->nullable();
            $table->integer('attempts')->default(1);
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            
            $table->timestamps();

            // Indexes for performance
            $table->index(['event_type', 'created_at']);
            $table->index(['queue', 'status', 'created_at']);
            $table->index(['job_class', 'status', 'created_at']);
            $table->index(['status', 'created_at']);
            $table->index(['created_at', 'execution_time']);
            
            // Composite indexes for analytics queries
            $table->index(['queue', 'job_class', 'created_at']);
            $table->index(['event_type', 'status', 'created_at']);
        });

        // Queue Performance Metrics - Aggregated performance data
        Schema::create('queue_performance_metrics', function (Blueprint $table) {
            $table->id();
            $table->string('queue', 100)->index();
            $table->string('job_class', 255)->nullable()->index();
            $table->date('metric_date')->index();
            $table->string('metric_period', 20)->index(); // hour, day, week, month
            $table->timestamp('period_start');
            $table->timestamp('period_end');
            
            // Job counts
            $table->integer('total_jobs')->default(0);
            $table->integer('completed_jobs')->default(0);
            $table->integer('failed_jobs')->default(0);
            $table->integer('retried_jobs')->default(0);
            
            // Performance metrics
            $table->decimal('avg_execution_time', 10, 3)->default(0);
            $table->decimal('min_execution_time', 10, 3)->default(0);
            $table->decimal('max_execution_time', 10, 3)->default(0);
            $table->decimal('avg_memory_usage', 15, 2)->default(0); // bytes
            $table->decimal('max_memory_usage', 15, 2)->default(0);
            $table->decimal('avg_queue_wait_time', 10, 3)->default(0);
            
            // Throughput metrics
            $table->decimal('jobs_per_second', 8, 3)->default(0);
            $table->decimal('jobs_per_minute', 8, 2)->default(0);
            $table->decimal('jobs_per_hour', 8, 2)->default(0);
            
            // Success metrics
            $table->decimal('success_rate', 5, 4)->default(0); // 0.0000 to 1.0000
            $table->decimal('failure_rate', 5, 4)->default(0);
            $table->decimal('retry_rate', 5, 4)->default(0);
            
            // Resource utilization
            $table->decimal('avg_cpu_usage', 5, 2)->default(0);
            $table->decimal('peak_cpu_usage', 5, 2)->default(0);
            $table->integer('active_workers')->default(0);
            
            $table->timestamps();

            // Unique constraint
            $table->unique(['queue', 'job_class', 'metric_date', 'metric_period'], 'queue_metrics_unique');
            
            // Indexes
            $table->index(['metric_date', 'metric_period']);
            $table->index(['queue', 'metric_date']);
            $table->index(['success_rate', 'metric_date']);
        });

        // Queue Health Snapshots - Point-in-time health status
        Schema::create('queue_health_snapshots', function (Blueprint $table) {
            $table->id();
            $table->timestamp('snapshot_time')->index();
            $table->string('overall_status', 20); // healthy, warning, critical
            
            // Queue status
            $table->json('queue_status'); // Per-queue health information
            $table->integer('total_pending_jobs')->default(0);
            $table->integer('total_processing_jobs')->default(0);
            $table->integer('total_failed_jobs')->default(0);
            
            // Worker status
            $table->integer('active_workers')->default(0);
            $table->integer('idle_workers')->default(0);
            $table->integer('busy_workers')->default(0);
            
            // System metrics
            $table->decimal('avg_memory_usage_mb', 10, 2)->default(0);
            $table->decimal('avg_cpu_usage_percent', 5, 2)->default(0);
            $table->decimal('avg_response_time_ms', 10, 3)->default(0);
            
            // Alerts and issues
            $table->json('active_alerts')->nullable();
            $table->json('performance_issues')->nullable();
            $table->json('recommendations')->nullable();
            
            // Metadata
            $table->json('metadata')->nullable();
            
            $table->timestamps();

            // Indexes
            $table->index(['snapshot_time', 'overall_status']);
            $table->index('overall_status');
        });

        // Queue Alerts - Alert history and configuration
        Schema::create('queue_alerts', function (Blueprint $table) {
            $table->id();
            $table->string('alert_type', 100)->index(); // high_failure_rate, queue_backlog, worker_down, etc.
            $table->string('severity', 20)->index(); // info, warning, critical
            $table->string('queue', 100)->nullable()->index();
            $table->string('job_class', 255)->nullable()->index();
            
            // Alert details
            $table->string('title', 255);
            $table->text('description');
            $table->json('alert_data'); // Specific alert information
            
            // Status tracking
            $table->string('status', 20)->default('active')->index(); // active, acknowledged, resolved
            $table->timestamp('triggered_at')->index();
            $table->timestamp('acknowledged_at')->nullable();
            $table->timestamp('resolved_at')->nullable();
            
            // Thresholds and conditions
            $table->json('trigger_conditions'); // Conditions that triggered the alert
            $table->decimal('threshold_value', 15, 4)->nullable();
            $table->decimal('actual_value', 15, 4)->nullable();
            
            // Assignment and notifications
            $table->string('assigned_to', 100)->nullable();
            $table->json('notification_channels')->nullable(); // email, slack, etc.
            $table->boolean('notifications_sent')->default(false);
            
            $table->timestamps();

            // Indexes
            $table->index(['alert_type', 'status', 'triggered_at']);
            $table->index(['severity', 'status']);
            $table->index(['queue', 'status']);
            $table->index(['triggered_at', 'status']);
        });

        // Queue Worker Statistics - Worker performance tracking
        Schema::create('queue_worker_stats', function (Blueprint $table) {
            $table->id();
            $table->string('worker_id', 100)->index();
            $table->string('queue', 100)->index();
            $table->string('connection', 50)->nullable();
            $table->timestamp('started_at');
            $table->timestamp('last_activity')->nullable();
            
            // Worker performance
            $table->integer('jobs_processed')->default(0);
            $table->integer('jobs_failed')->default(0);
            $table->decimal('avg_job_time', 10, 3)->default(0);
            $table->decimal('total_processing_time', 15, 3)->default(0);
            
            // Resource usage
            $table->decimal('avg_memory_usage', 15, 2)->default(0);
            $table->decimal('peak_memory_usage', 15, 2)->default(0);
            $table->decimal('avg_cpu_usage', 5, 2)->default(0);
            
            // Worker status
            $table->string('status', 20)->default('active')->index(); // active, idle, stopped, error
            $table->timestamp('status_changed_at')->nullable();
            $table->text('last_error')->nullable();
            
            // Configuration
            $table->json('worker_config')->nullable(); // Worker configuration details
            $table->string('supervisor', 100)->nullable(); // Horizon supervisor name
            
            $table->timestamps();

            // Indexes
            $table->index(['worker_id', 'status']);
            $table->index(['queue', 'status', 'last_activity']);
            $table->index(['started_at', 'status']);
        });

        // Queue Batch Analytics - Batch job performance tracking
        Schema::create('queue_batch_analytics', function (Blueprint $table) {
            $table->id();
            $table->string('batch_id', 100)->index();
            $table->string('batch_name', 255)->nullable()->index();
            $table->string('queue', 100)->nullable()->index();
            
            // Batch metrics
            $table->integer('total_jobs')->default(0);
            $table->integer('completed_jobs')->default(0);
            $table->integer('failed_jobs')->default(0);
            $table->integer('cancelled_jobs')->default(0);
            
            // Timing
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->decimal('total_duration', 15, 3)->nullable(); // seconds
            $table->decimal('avg_job_duration', 10, 3)->nullable();
            
            // Performance
            $table->decimal('throughput_jobs_per_second', 8, 3)->default(0);
            $table->decimal('avg_memory_usage', 15, 2)->default(0);
            $table->decimal('peak_memory_usage', 15, 2)->default(0);
            
            // Progress tracking
            $table->decimal('progress_percentage', 5, 2)->default(0);
            $table->timestamp('last_progress_update')->nullable();
            
            // Status and metadata
            $table->string('status', 20)->default('pending')->index(); // pending, processing, completed, failed, cancelled
            $table->json('metadata')->nullable();
            $table->json('failure_summary')->nullable(); // Summary of failures if any
            
            $table->timestamps();

            // Indexes
            $table->index(['batch_name', 'status']);
            $table->index(['started_at', 'status']);
            $table->index(['queue', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('queue_batch_analytics');
        Schema::dropIfExists('queue_worker_stats');
        Schema::dropIfExists('queue_alerts');
        Schema::dropIfExists('queue_health_snapshots');
        Schema::dropIfExists('queue_performance_metrics');
        Schema::dropIfExists('queue_events');
    }
};
