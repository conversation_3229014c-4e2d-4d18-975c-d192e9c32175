<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
// use App\Http\Controllers\Api\V1\Analytics\AnalyticsController;
// use App\Http\Controllers\Api\V1\Search\SearchController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Sepet sayısı API'si
Route::get('/cart/count', [\App\Http\Controllers\CartController::class, 'getCartCount']);

// Search & Analytics API Routes - Geçici olarak devre dışı
/*
Route::prefix('v1')->group(function () {

    // Search API
    Route::prefix('search')->group(function () {
        Route::get('/products', [SearchController::class, 'searchProducts']);
        Route::get('/autocomplete', [SearchController::class, 'autocomplete']);
        Route::get('/suggestions', [SearchController::class, 'suggestions']);
        Route::get('/stats', [SearchController::class, 'searchStats']);
    });

    // Analytics API
    Route::prefix('analytics')->group(function () {
        Route::get('/dashboard', [AnalyticsController::class, 'dashboard']);
        Route::get('/real-time', [AnalyticsController::class, 'realTime']);
        Route::get('/conversion-funnel', [AnalyticsController::class, 'conversionFunnel']);
        Route::get('/product-performance/{productId}', [AnalyticsController::class, 'productPerformance']);
        Route::get('/search-analytics', [AnalyticsController::class, 'searchAnalytics']);
        Route::get('/trending-products', [AnalyticsController::class, 'trendingProducts']);
        Route::get('/popular-searches', [AnalyticsController::class, 'popularSearches']);
        Route::get('/last-minute-metrics', [AnalyticsController::class, 'lastMinuteMetrics']);
        Route::get('/index-stats', [AnalyticsController::class, 'indexStats']);
        Route::get('/custom-report', [AnalyticsController::class, 'customReport']);

        // Event recording
        Route::post('/event', [AnalyticsController::class, 'recordEvent']);

        // Admin only routes
        Route::middleware(['auth:sanctum', 'role:admin'])->group(function () {
            Route::delete('/cache', [AnalyticsController::class, 'clearCache']);
        });
    });
});
*/

// Ürün arama API'si
Route::get('/products/search', [\App\Http\Controllers\Api\ProductController::class, 'search']);

// Ürün API rotaları - Sadece belirli metodları aç
Route::apiResource('products', \App\Http\Controllers\Api\ProductController::class, [
    'only' => ['index', 'store', 'update', 'destroy']
]);

// Ürün detayı için özel rota - /api öneki ile
Route::get('/api-products/{id}', [\App\Http\Controllers\Api\ProductController::class, 'show']);

// Hızlı bakış için ürün detayı - API öneki olmadan
Route::get('/products/{id}/details', [\App\Http\Controllers\Api\ProductController::class, 'getProductDetails']);

// API v1 rotaları
Route::prefix('v1')->group(function () {
    // Gelecekteki modül sistemi için API rotaları buraya eklenecek
});

// Konum yönetimi için API rotaları artık web.php'de tanımlanmıştır

// Kargo hesaplama API rotaları
Route::post('/shipping/available-methods', [\App\Http\Controllers\Api\ShippingController::class, 'getAvailableMethods']);
Route::post('/shipping/calculate-cost', [\App\Http\Controllers\Api\ShippingController::class, 'calculateShippingCost']);

// Payment API rotaları (temporarily disabled due to missing dependencies)
// require __DIR__ . '/api/payment.php';

// Phase 4: Frontend Integration - CQRS API Routes
require __DIR__.'/api/cqrs.php';
