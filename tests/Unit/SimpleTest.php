<?php

namespace Tests\Unit;

use Tests\TestCase;

class SimpleTest extends TestCase
{
    /**
     * Test basic Laravel functionality
     */
    public function test_application_loads(): void
    {
        $this->assertTrue(true);
    }

    /**
     * Test config loading
     */
    public function test_config_loads(): void
    {
        $appName = config('app.name');
        $this->assertNotEmpty($appName);
    }

    /**
     * Test database connection (without migration)
     */
    public function test_database_connection(): void
    {
        // Test database connection without running migrations
        $connection = \DB::connection();
        $this->assertNotNull($connection);

        // Test that we can execute a simple query
        $result = \DB::select('SELECT 1 as test');
        $this->assertEquals(1, $result[0]->test);
    }
}
