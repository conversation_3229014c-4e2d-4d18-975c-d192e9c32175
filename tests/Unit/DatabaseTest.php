<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class DatabaseTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test basic database functionality with migrations
     */
    public function test_database_with_migrations(): void
    {
        // Test that migrations run successfully
        $this->assertTrue(true);
        
        // Test that we can check table existence
        $this->assertTrue(\Schema::hasTable('users'));
    }

    /**
     * Test user creation
     */
    public function test_user_creation(): void
    {
        // Test basic user creation
        $user = \App\Models\User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'Test User',
        ]);

        $this->assertEquals('Test User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
    }
}
