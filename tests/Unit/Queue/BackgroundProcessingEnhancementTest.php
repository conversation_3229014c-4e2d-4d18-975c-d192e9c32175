<?php

namespace Tests\Unit\Queue;

use App\Core\Infrastructure\Queue\Services\JobPriorityManager;
use App\Core\Infrastructure\Queue\Services\BatchJobManager;
use App\Core\Infrastructure\Queue\Services\QueueHealthMonitor;
use App\Core\Infrastructure\Queue\Contracts\PrioritizableJobInterface;
use App\Core\Infrastructure\Queue\Contracts\BatchProcessableInterface;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Bus;
use Tests\TestCase;

/**
 * Background Processing Enhancement Test
 * Phase 5C: Background Processing Enhancement test sınıfı
 */
class BackgroundProcessingEnhancementTest extends TestCase
{
    use RefreshDatabase;

    protected JobPriorityManager $priorityManager;
    protected BatchJobManager $batchManager;
    protected QueueHealthMonitor $healthMonitor;

    protected function setUp(): void
    {
        parent::setUp();

        $this->priorityManager = app(JobPriorityManager::class);
        $this->batchManager = app(BatchJobManager::class);
        $this->healthMonitor = app(QueueHealthMonitor::class);
    }

    /** @test */
    public function it_can_route_jobs_by_priority()
    {
        // Arrange
        $job = new TestPrioritizableJob(priority: 1, category: 'payment', businessImpact: 'critical');

        // Act
        $queueName = $this->priorityManager->routeJob($job);

        // Assert
        $this->assertStringContains('critical', $queueName);
    }

    /** @test */
    public function it_can_calculate_dynamic_priority()
    {
        // Arrange
        $job = new TestPrioritizableJob(priority: 5, category: 'order', businessImpact: 'high');

        // Act
        $dynamicPriority = $this->priorityManager->calculateDynamicPriority($job);

        // Assert
        $this->assertIsInt($dynamicPriority);
        $this->assertGreaterThanOrEqual(1, $dynamicPriority);
        $this->assertLessThanOrEqual(10, $dynamicPriority);
    }

    /** @test */
    public function it_can_detect_queue_overload()
    {
        // Arrange
        $queueName = 'test_queue';

        // Act
        $isOverloaded = $this->priorityManager->isQueueOverloaded($queueName);

        // Assert
        $this->assertIsBool($isOverloaded);
    }

    /** @test */
    public function it_can_find_alternative_queue()
    {
        // Arrange
        $originalQueue = 'high';
        $priority = 3;

        // Act
        $alternativeQueue = $this->priorityManager->findAlternativeQueue($originalQueue, $priority);

        // Assert
        $this->assertIsString($alternativeQueue);
        $this->assertNotEmpty($alternativeQueue);
    }

    /** @test */
    public function it_can_create_batch_job()
    {
        // Arrange
        $jobs = [
            new TestBatchableJob(['data' => 'test1']),
            new TestBatchableJob(['data' => 'test2']),
            new TestBatchableJob(['data' => 'test3']),
        ];

        // Act
        $batch = $this->batchManager->createBatch('test_batch', $jobs);

        // Assert
        $this->assertNotNull($batch);
        $this->assertEquals('test_batch', $batch->name);
        $this->assertEquals(3, $batch->totalJobs);
    }

    /** @test */
    public function it_can_create_chunked_batch()
    {
        // Arrange
        $data = collect(range(1, 250)); // 250 items
        $jobClass = TestBatchableJob::class;
        $options = ['chunk_size' => 50];

        // Act
        $batch = $this->batchManager->createChunkedBatch('chunked_test', $data, $jobClass, $options);

        // Assert
        $this->assertNotNull($batch);
        $this->assertEquals('chunked_test', $batch->name);
        $this->assertEquals(5, $batch->totalJobs); // 250/50 = 5 chunks
    }

    /** @test */
    public function it_can_get_batch_progress()
    {
        // Arrange
        $jobs = [new TestBatchableJob(['data' => 'test'])];
        $batch = $this->batchManager->createBatch('progress_test', $jobs);

        // Act
        $progress = $this->batchManager->getBatchProgress($batch->id);

        // Assert
        $this->assertIsArray($progress);
        $this->assertArrayHasKey('id', $progress);
        $this->assertArrayHasKey('total_jobs', $progress);
        $this->assertArrayHasKey('progress_percentage', $progress);
    }

    /** @test */
    public function it_can_cancel_batch()
    {
        // Arrange
        $jobs = [new TestBatchableJob(['data' => 'test'])];
        $batch = $this->batchManager->createBatch('cancel_test', $jobs);

        // Act
        $result = $this->batchManager->cancelBatch($batch->id);

        // Assert
        $this->assertTrue($result);
    }

    /** @test */
    public function it_can_get_batch_statistics()
    {
        // Act
        $stats = $this->batchManager->getBatchStatistics();

        // Assert
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total_batches', $stats);
        $this->assertArrayHasKey('total_jobs', $stats);
        $this->assertArrayHasKey('success_rate', $stats);
    }

    /** @test */
    public function it_can_check_overall_health()
    {
        // Act
        $healthReport = $this->healthMonitor->checkOverallHealth();

        // Assert
        $this->assertIsArray($healthReport);
        $this->assertArrayHasKey('overall_status', $healthReport);
        $this->assertArrayHasKey('checks', $healthReport);
        $this->assertArrayHasKey('timestamp', $healthReport);
        $this->assertContains($healthReport['overall_status'], ['healthy', 'warning', 'critical', 'error']);
    }

    /** @test */
    public function it_can_check_queue_connectivity()
    {
        // Act
        $connectivityCheck = $this->healthMonitor->checkQueueConnectivity();

        // Assert
        $this->assertIsArray($connectivityCheck);
        $this->assertArrayHasKey('status', $connectivityCheck);
        $this->assertArrayHasKey('connections', $connectivityCheck);
    }

    /** @test */
    public function it_can_check_queue_sizes()
    {
        // Act
        $queueSizeCheck = $this->healthMonitor->checkQueueSizes();

        // Assert
        $this->assertIsArray($queueSizeCheck);
        $this->assertArrayHasKey('status', $queueSizeCheck);
        $this->assertArrayHasKey('queues', $queueSizeCheck);
    }

    /** @test */
    public function it_can_check_worker_status()
    {
        // Act
        $workerCheck = $this->healthMonitor->checkWorkerStatus();

        // Assert
        $this->assertIsArray($workerCheck);
        $this->assertArrayHasKey('status', $workerCheck);
    }

    /** @test */
    public function it_can_check_failed_jobs()
    {
        // Act
        $failedJobsCheck = $this->healthMonitor->checkFailedJobs();

        // Assert
        $this->assertIsArray($failedJobsCheck);
        $this->assertArrayHasKey('status', $failedJobsCheck);
        $this->assertArrayHasKey('total_failed', $failedJobsCheck);
    }

    /** @test */
    public function it_can_check_memory_usage()
    {
        // Act
        $memoryCheck = $this->healthMonitor->checkMemoryUsage();

        // Assert
        $this->assertIsArray($memoryCheck);
        $this->assertArrayHasKey('status', $memoryCheck);
        $this->assertArrayHasKey('current_usage', $memoryCheck);
        $this->assertArrayHasKey('usage_percent', $memoryCheck);
    }
}

/**
 * Test Prioritizable Job
 */
class TestPrioritizableJob implements PrioritizableJobInterface
{
    public function __construct(
        private int $priority = 5,
        private string $category = 'default',
        private string $businessImpact = 'medium',
        private int $sla = 300
    ) {}

    public function getPriority(): int { return $this->priority; }
    public function getSLA(): int { return $this->sla; }
    public function getJobCategory(): string { return $this->category; }
    public function getBusinessImpact(): string { return $this->businessImpact; }
    public function getResourceRequirements(): array { return []; }
    public function getDependencies(): array { return []; }
    public function getDeadline(): ?\DateTimeInterface { return null; }
    public function getRetryStrategy(): array { return []; }
    public function getEscalationRules(): array { return []; }
    public function calculateDynamicPriority(array $context = []): int { return $this->priority; }
    public function getQueueRouting(): array { return []; }
}

/**
 * Test Batchable Job
 */
class TestBatchableJob implements BatchProcessableInterface
{
    public function __construct(private array $data = []) {}

    public function getBatchSize(): int { return 100; }
    public function getBatchTimeout(): int { return 300; }
    public function canProcessInParallel(): bool { return true; }
    public function canChunk(): bool { return true; }
    public function getChunkSize(): int { return 50; }
    public function requiresProgressTracking(): bool { return true; }
    public function getFailureHandlingStrategy(): string { return 'continue'; }
    public function getBatchCompletionCallback(): ?callable { return null; }
    public function getBatchProgressCallback(): ?callable { return null; }
    public function isRetryable(): bool { return true; }
    public function getBatchRetryStrategy(): array { return []; }
    public function getBatchDependencies(): array { return []; }
    public function getBatchResourceRequirements(): array { return []; }
}
