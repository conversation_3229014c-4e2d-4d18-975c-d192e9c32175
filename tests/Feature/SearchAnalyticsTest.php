<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Core\Infrastructure\Search\ElasticsearchService;
use App\Core\Infrastructure\Search\SearchIndexManager;
use App\Core\Infrastructure\Analytics\AnalyticsEngine;
use App\Core\Infrastructure\Analytics\RealTimeProcessor;
use App\Models\Product;
use App\Models\Category;
use App\Models\User;

class SearchAnalyticsTest extends TestCase
{
    use RefreshDatabase;

    protected ElasticsearchService $elasticsearch;
    protected SearchIndexManager $indexManager;
    protected AnalyticsEngine $analyticsEngine;
    protected RealTimeProcessor $realTimeProcessor;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->elasticsearch = app(ElasticsearchService::class);
        $this->indexManager = app(SearchIndexManager::class);
        $this->analyticsEngine = app(AnalyticsEngine::class);
        $this->realTimeProcessor = app(RealTimeProcessor::class);
    }

    /** @test */
    public function it_can_create_elasticsearch_indices()
    {
        $result = $this->indexManager->createAllIndices();
        $this->assertTrue($result);
    }

    /** @test */
    public function it_can_index_products()
    {
        // Test verileri oluştur
        $category = Category::factory()->create(['name' => 'Test Category']);
        $products = Product::factory()->count(5)->create([
            'category_id' => $category->id,
            'status' => 'active'
        ]);

        // Ürünleri indexle
        $result = $this->indexManager->indexProducts($products);
        $this->assertTrue($result);
    }

    /** @test */
    public function it_can_search_products_via_api()
    {
        // Test verileri oluştur
        $category = Category::factory()->create(['name' => 'Electronics']);
        $product = Product::factory()->create([
            'name' => 'Test Laptop',
            'category_id' => $category->id,
            'status' => 'active'
        ]);

        // API üzerinden arama yap
        $response = $this->getJson('/api/v1/search/products?q=laptop');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'query',
                        'pagination',
                        'products',
                        'took'
                    ]
                ]);
    }

    /** @test */
    public function it_can_record_analytics_events()
    {
        $user = User::factory()->create();
        $product = Product::factory()->create();

        // Event kaydet
        $result = $this->analyticsEngine->recordEvent('product_view', [
            'product_id' => $product->id,
            'user_id' => $user->id,
        ]);

        $this->assertTrue($result);
    }

    /** @test */
    public function it_can_process_real_time_events()
    {
        $eventData = [
            'event_type' => 'product_view',
            'product_id' => 1,
            'user_id' => 1,
            'timestamp' => now()->toISOString(),
        ];

        // Real-time event işle
        $this->realTimeProcessor->processEvent($eventData);

        // Metrikleri kontrol et
        $metrics = $this->realTimeProcessor->getRealTimeMetrics();
        $this->assertIsArray($metrics);
        $this->assertArrayHasKey('current_metrics', $metrics);
    }

    /** @test */
    public function it_can_get_dashboard_analytics()
    {
        $response = $this->getJson('/api/v1/analytics/dashboard');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'real_time',
                        'conversion_funnel',
                        'search_analytics',
                        'trending_products',
                        'popular_searches'
                    ]
                ]);
    }

    /** @test */
    public function it_can_get_real_time_metrics()
    {
        $response = $this->getJson('/api/v1/analytics/real-time');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'current_metrics',
                        'hourly_metrics',
                        'trending'
                    ],
                    'timestamp'
                ]);
    }

    /** @test */
    public function it_can_autocomplete_search()
    {
        $response = $this->getJson('/api/v1/search/autocomplete?q=test');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'query',
                        'products',
                        'categories',
                        'suggestions'
                    ]
                ]);
    }

    /** @test */
    public function it_can_get_search_suggestions()
    {
        $response = $this->getJson('/api/v1/search/suggestions?q=laptop');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'query',
                        'type',
                        'suggestions'
                    ]
                ]);
    }

    /** @test */
    public function it_can_record_events_via_api()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $eventData = [
            'event_type' => 'product_view',
            'data' => [
                'product_id' => 1,
                'category_id' => 1,
            ]
        ];

        $response = $this->postJson('/api/v1/analytics/event', $eventData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Event başarıyla kaydedildi'
                ]);
    }

    /** @test */
    public function it_can_get_trending_products()
    {
        $response = $this->getJson('/api/v1/analytics/trending-products?limit=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data',
                    'limit'
                ]);
    }

    /** @test */
    public function it_can_get_popular_searches()
    {
        $response = $this->getJson('/api/v1/analytics/popular-searches?limit=10');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data',
                    'limit'
                ]);
    }

    /** @test */
    public function it_can_get_conversion_funnel()
    {
        $response = $this->getJson('/api/v1/analytics/conversion-funnel?start_date=2024-01-01&end_date=2024-01-31');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data',
                    'period'
                ]);
    }

    /** @test */
    public function it_can_get_search_analytics()
    {
        $response = $this->getJson('/api/v1/analytics/search-analytics?start_date=2024-01-01&end_date=2024-01-31');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data'
                ]);
    }

    /** @test */
    public function it_can_get_product_performance()
    {
        $product = Product::factory()->create();

        $response = $this->getJson("/api/v1/analytics/product-performance/{$product->id}?start_date=2024-01-01&end_date=2024-01-31");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data'
                ]);
    }

    /** @test */
    public function it_can_get_index_statistics()
    {
        $response = $this->getJson('/api/v1/analytics/index-stats');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'indices',
                        'cluster'
                    ]
                ]);
    }

    /** @test */
    public function it_can_create_custom_reports()
    {
        $reportData = [
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'metrics' => ['conversion_funnel', 'search_analytics'],
            'filters' => []
        ];

        $response = $this->getJson('/api/v1/analytics/custom-report?' . http_build_query($reportData));

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'period',
                        'filters',
                        'data'
                    ]
                ]);
    }

    /** @test */
    public function it_validates_search_parameters()
    {
        // Geçersiz parametrelerle arama
        $response = $this->getJson('/api/v1/search/products?per_page=1000');

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['per_page']);
    }

    /** @test */
    public function it_validates_analytics_parameters()
    {
        // Geçersiz tarih aralığı
        $response = $this->getJson('/api/v1/analytics/conversion-funnel?start_date=2024-01-31&end_date=2024-01-01');

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['end_date']);
    }

    /** @test */
    public function it_handles_elasticsearch_connection_errors_gracefully()
    {
        // Elasticsearch bağlantısı olmadığında fallback davranışını test et
        $response = $this->getJson('/api/v1/search/products?q=test');

        // Hata durumunda bile 200 dönmeli (database fallback)
        $response->assertStatus(200);
    }
}
