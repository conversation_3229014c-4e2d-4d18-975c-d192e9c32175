<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="CLI Arguments" tests="21" assertions="49" errors="0" failures="1" skipped="0" time="1.903548">
    <testsuite name="Tests\Unit\Domain\Products\Services\ProductDomainServiceTest" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php" tests="6" assertions="15" errors="0" failures="0" skipped="0" time="0.826513">
      <testcase name="it_can_increment_view_count" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php" line="174" class="Tests\Unit\Domain\Products\Services\ProductDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.ProductDomainServiceTest" assertions="1" time="0.555300"/>
      <testcase name="it_can_create_a_product" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php" line="34" class="Tests\Unit\Domain\Products\Services\ProductDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.ProductDomainServiceTest" assertions="6" time="0.055858"/>
      <testcase name="it_can_update_product_basic_info" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php" line="134" class="Tests\Unit\Domain\Products\Services\ProductDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.ProductDomainServiceTest" assertions="3" time="0.050909"/>
      <testcase name="it_can_update_product_price" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php" line="159" class="Tests\Unit\Domain\Products\Services\ProductDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.ProductDomainServiceTest" assertions="1" time="0.059911"/>
      <testcase name="it_throws_exception_when_slug_already_exists" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php" line="101" class="Tests\Unit\Domain\Products\Services\ProductDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.ProductDomainServiceTest" assertions="2" time="0.053394"/>
      <testcase name="it_throws_exception_when_sku_already_exists" file="C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php" line="74" class="Tests\Unit\Domain\Products\Services\ProductDomainServiceTest" classname="Tests.Unit.Domain.Products.Services.ProductDomainServiceTest" assertions="2" time="0.051141"/>
    </testsuite>
    <testsuite name="Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php" tests="13" assertions="31" errors="0" failures="0" skipped="0" time="0.888509">
      <testcase name="it_handles_object_values" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php" line="138" class="Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest" classname="Tests.Unit.Core.Infrastructure.Cache.Services.CacheKeyGeneratorTest" assertions="1" time="0.053016"/>
      <testcase name="it_handles_long_keys" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php" line="98" class="Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest" classname="Tests.Unit.Core.Infrastructure.Cache.Services.CacheKeyGeneratorTest" assertions="3" time="0.062677"/>
      <testcase name="it_generates_query_key" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php" line="38" class="Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest" classname="Tests.Unit.Core.Infrastructure.Cache.Services.CacheKeyGeneratorTest" assertions="3" time="0.054145"/>
      <testcase name="it_generates_stats_key" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php" line="48" class="Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest" classname="Tests.Unit.Core.Infrastructure.Cache.Services.CacheKeyGeneratorTest" assertions="3" time="0.059529"/>
      <testcase name="it_sorts_criteria_for_consistency" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php" line="151" class="Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest" classname="Tests.Unit.Core.Infrastructure.Cache.Services.CacheKeyGeneratorTest" assertions="1" time="0.059420"/>
      <testcase name="it_generates_list_key" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php" line="27" class="Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest" classname="Tests.Unit.Core.Infrastructure.Cache.Services.CacheKeyGeneratorTest" assertions="4" time="0.071097"/>
      <testcase name="it_generates_entity_key" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php" line="19" class="Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest" classname="Tests.Unit.Core.Infrastructure.Cache.Services.CacheKeyGeneratorTest" assertions="1" time="0.053410"/>
      <testcase name="it_generates_date_range_key" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php" line="58" class="Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest" classname="Tests.Unit.Core.Infrastructure.Cache.Services.CacheKeyGeneratorTest" assertions="4" time="0.057940"/>
      <testcase name="it_generates_custom_key" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php" line="72" class="Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest" classname="Tests.Unit.Core.Infrastructure.Cache.Services.CacheKeyGeneratorTest" assertions="1" time="0.062454"/>
      <testcase name="it_extracts_prefix" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php" line="90" class="Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest" classname="Tests.Unit.Core.Infrastructure.Cache.Services.CacheKeyGeneratorTest" assertions="1" time="0.064288"/>
      <testcase name="it_validates_keys" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php" line="80" class="Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest" classname="Tests.Unit.Core.Infrastructure.Cache.Services.CacheKeyGeneratorTest" assertions="5" time="0.104733"/>
      <testcase name="it_handles_array_values_in_criteria" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php" line="123" class="Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest" classname="Tests.Unit.Core.Infrastructure.Cache.Services.CacheKeyGeneratorTest" assertions="2" time="0.109848"/>
      <testcase name="it_normalizes_entity_types" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php" line="112" class="Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest" classname="Tests.Unit.Core.Infrastructure.Cache.Services.CacheKeyGeneratorTest" assertions="2" time="0.075951"/>
    </testsuite>
    <testsuite name="Tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest.php" tests="2" assertions="3" errors="0" failures="1" skipped="0" time="0.188526">
      <testcase name="it_can_set_and_get_context" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest.php" line="110" class="Tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest" classname="Tests.Unit.Core.Infrastructure.Api.ApiResourceTransformerTest" assertions="1" time="0.095807"/>
      <testcase name="it_can_transform_datetime_objects" file="C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest.php" line="77" class="Tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest" classname="Tests.Unit.Core.Infrastructure.Api.ApiResourceTransformerTest" assertions="2" time="0.092719">
        <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest::it_can_transform_datetime_objects&#13;
Failed asserting that an array has the key 'value'.
&#13;
C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest.php:84</failure>
      </testcase>
    </testsuite>
  </testsuite>
</testsuites>
