##teamcity[testCount count='627' flowId='22896']
##teamcity[testSuiteStarted name='CLI Arguments' flowId='22896']
##teamcity[testSuiteStarted name='Tests\Unit\Domain\Products\Services\ProductDomainServiceTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\ProductDomainServiceTest' flowId='22896']
##teamcity[testStarted name='it_can_increment_view_count' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\ProductDomainServiceTest::it_can_increment_view_count' flowId='22896']
##teamcity[testFinished name='it_can_increment_view_count' duration='15' flowId='22896']
##teamcity[testStarted name='it_can_create_a_product' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\ProductDomainServiceTest::it_can_create_a_product' flowId='22896']
##teamcity[testFinished name='it_can_create_a_product' duration='7' flowId='22896']
##teamcity[testStarted name='it_can_update_product_basic_info' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\ProductDomainServiceTest::it_can_update_product_basic_info' flowId='22896']
##teamcity[testFinished name='it_can_update_product_basic_info' duration='2' flowId='22896']
##teamcity[testStarted name='it_can_update_product_price' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\ProductDomainServiceTest::it_can_update_product_price' flowId='22896']
##teamcity[testFinished name='it_can_update_product_price' duration='4' flowId='22896']
##teamcity[testStarted name='it_throws_exception_when_slug_already_exists' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\ProductDomainServiceTest::it_throws_exception_when_slug_already_exists' flowId='22896']
##teamcity[testFinished name='it_throws_exception_when_slug_already_exists' duration='5' flowId='22896']
##teamcity[testStarted name='it_throws_exception_when_sku_already_exists' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Domain\Products\Services\ProductDomainServiceTest.php::\Tests\Unit\Domain\Products\Services\ProductDomainServiceTest::it_throws_exception_when_sku_already_exists' flowId='22896']
##teamcity[testFinished name='it_throws_exception_when_sku_already_exists' duration='2' flowId='22896']
##teamcity[testSuiteFinished name='Tests\Unit\Domain\Products\Services\ProductDomainServiceTest' flowId='22896']
##teamcity[testSuiteStarted name='Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php::\Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest' flowId='22896']
##teamcity[testStarted name='it_handles_object_values' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php::\Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest::it_handles_object_values' flowId='22896']
##teamcity[testFinished name='it_handles_object_values' duration='2' flowId='22896']
##teamcity[testStarted name='it_handles_long_keys' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php::\Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest::it_handles_long_keys' flowId='22896']
##teamcity[testFinished name='it_handles_long_keys' duration='7' flowId='22896']
##teamcity[testStarted name='it_generates_query_key' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php::\Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest::it_generates_query_key' flowId='22896']
##teamcity[testFinished name='it_generates_query_key' duration='3' flowId='22896']
##teamcity[testStarted name='it_generates_stats_key' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php::\Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest::it_generates_stats_key' flowId='22896']
##teamcity[testFinished name='it_generates_stats_key' duration='2' flowId='22896']
##teamcity[testStarted name='it_sorts_criteria_for_consistency' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php::\Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest::it_sorts_criteria_for_consistency' flowId='22896']
##teamcity[testFinished name='it_sorts_criteria_for_consistency' duration='3' flowId='22896']
##teamcity[testStarted name='it_generates_list_key' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php::\Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest::it_generates_list_key' flowId='22896']
##teamcity[testFinished name='it_generates_list_key' duration='3' flowId='22896']
##teamcity[testStarted name='it_generates_entity_key' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php::\Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest::it_generates_entity_key' flowId='22896']
##teamcity[testFinished name='it_generates_entity_key' duration='2' flowId='22896']
##teamcity[testStarted name='it_generates_date_range_key' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php::\Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest::it_generates_date_range_key' flowId='22896']
##teamcity[testFinished name='it_generates_date_range_key' duration='5' flowId='22896']
##teamcity[testStarted name='it_generates_custom_key' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php::\Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest::it_generates_custom_key' flowId='22896']
##teamcity[testFinished name='it_generates_custom_key' duration='3' flowId='22896']
##teamcity[testStarted name='it_extracts_prefix' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php::\Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest::it_extracts_prefix' flowId='22896']
##teamcity[testFinished name='it_extracts_prefix' duration='2' flowId='22896']
##teamcity[testStarted name='it_validates_keys' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php::\Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest::it_validates_keys' flowId='22896']
##teamcity[testFinished name='it_validates_keys' duration='12' flowId='22896']
##teamcity[testStarted name='it_handles_array_values_in_criteria' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php::\Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest::it_handles_array_values_in_criteria' flowId='22896']
##teamcity[testFinished name='it_handles_array_values_in_criteria' duration='9' flowId='22896']
##teamcity[testStarted name='it_normalizes_entity_types' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest.php::\Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest::it_normalizes_entity_types' flowId='22896']
##teamcity[testFinished name='it_normalizes_entity_types' duration='5' flowId='22896']
##teamcity[testSuiteFinished name='Tests\Unit\Core\Infrastructure\Cache\Services\CacheKeyGeneratorTest' flowId='22896']
##teamcity[testSuiteStarted name='Tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest.php::\Tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest' flowId='22896']
##teamcity[testStarted name='it_can_set_and_get_context' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest.php::\Tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest::it_can_set_and_get_context' flowId='22896']
##teamcity[testFinished name='it_can_set_and_get_context' duration='11' flowId='22896']
##teamcity[testStarted name='it_can_transform_datetime_objects' locationHint='php_qn://C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest.php::\Tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest::it_can_transform_datetime_objects' flowId='22896']
##teamcity[testFailed name='it_can_transform_datetime_objects' message='Failed asserting that an array has the key |'value|'.' details='C:\laragon\www\modularecommerce\tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest.php:84|n' duration='11' flowId='22896']
##teamcity[testFinished name='it_can_transform_datetime_objects' duration='16' flowId='22896']
##teamcity[testSuiteFinished name='Tests\Unit\Core\Infrastructure\Api\ApiResourceTransformerTest' flowId='22896']
##teamcity[testSuiteFinished name='CLI Arguments' flowId='22896']
