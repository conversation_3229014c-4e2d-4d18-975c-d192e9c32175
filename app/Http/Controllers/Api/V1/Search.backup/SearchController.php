<?php

namespace App\Http\Controllers\Api\V1\Search;

use App\Http\Controllers\Controller;
use App\Core\Infrastructure\Search\ElasticsearchService;
use App\Core\Infrastructure\Search\SearchIndexManager;
use App\Core\Infrastructure\Analytics\AnalyticsEngine;
use App\Infrastructure\Products\Services\ProductSearchService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * SearchController
 * Advanced search API endpoints
 */
class SearchController extends Controller
{
    protected ElasticsearchService $elasticsearch;
    protected SearchIndexManager $indexManager;
    protected AnalyticsEngine $analyticsEngine;
    protected ProductSearchService $productSearchService;

    public function __construct(
        ElasticsearchService $elasticsearch,
        SearchIndexManager $indexManager,
        AnalyticsEngine $analyticsEngine,
        ProductSearchService $productSearchService
    ) {
        $this->elasticsearch = $elasticsearch;
        $this->indexManager = $indexManager;
        $this->analyticsEngine = $analyticsEngine;
        $this->productSearchService = $productSearchService;
    }

    /**
     * Gelişmiş ürün arama
     */
    public function searchProducts(Request $request): JsonResponse
    {
        $request->validate([
            'q' => 'nullable|string|max:255',
            'category_id' => 'nullable|integer',
            'category_ids' => 'nullable|array',
            'category_ids.*' => 'integer',
            'brand' => 'nullable|string|max:100',
            'brands' => 'nullable|array',
            'brands.*' => 'string|max:100',
            'price_min' => 'nullable|numeric|min:0',
            'price_max' => 'nullable|numeric|min:0',
            'in_stock' => 'nullable|boolean',
            'sort' => 'nullable|string|in:relevance,price_asc,price_desc,name_asc,name_desc,created_at_desc,popularity,sales',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'include_aggregations' => 'nullable|boolean',
            'include_suggestions' => 'nullable|boolean',
        ]);

        try {
            $query = $request->input('q', '');
            $filters = $this->buildFilters($request);
            $sort = $this->buildSort($request->input('sort', 'relevance'));
            $page = $request->input('page', 1);
            $perPage = $request->input('per_page', 20);
            $offset = ($page - 1) * $perPage;

            // Analytics event kaydet
            if (!empty($query)) {
                $this->analyticsEngine->recordEvent('search_query', [
                    'search_query' => $query,
                    'filters' => $filters,
                    'page' => $page,
                ]);
            }

            // Arama parametrelerini hazırla
            $searchParams = [
                'query' => $query,
                'filters' => $filters,
                'sort' => $sort,
                'size' => $perPage,
                'from' => $offset,
            ];

            // Aggregations ekle
            if ($request->input('include_aggregations', true)) {
                $searchParams['aggregations'] = [
                    'categories' => [
                        'type' => 'terms',
                        'field' => 'category_id',
                        'size' => 20
                    ],
                    'brands' => [
                        'type' => 'terms',
                        'field' => 'brand.keyword',
                        'size' => 20
                    ],
                    'price_ranges' => [
                        'type' => 'range',
                        'field' => 'price',
                        'ranges' => [
                            ['key' => '0-100', 'to' => 100],
                            ['key' => '100-500', 'from' => 100, 'to' => 500],
                            ['key' => '500-1000', 'from' => 500, 'to' => 1000],
                            ['key' => '1000+', 'from' => 1000]
                        ]
                    ]
                ];
            }

            // Suggestions ekle
            if ($request->input('include_suggestions', true) && !empty($query)) {
                $searchParams['suggest'] = [
                    'product_suggest' => [
                        'text' => $query,
                        'field' => 'name',
                        'size' => 5
                    ]
                ];
            }

            $response = $this->elasticsearch->search($searchParams);

            // Sonuçları formatla
            $result = [
                'query' => $query,
                'filters' => $filters,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $response['total'],
                    'last_page' => ceil($response['total'] / $perPage),
                    'has_more' => ($offset + $perPage) < $response['total'],
                ],
                'products' => $response['hits'],
                'took' => $response['took'],
            ];

            if (isset($response['aggregations'])) {
                $result['facets'] = $this->formatAggregations($response['aggregations']);
            }

            if (isset($response['suggestions'])) {
                $result['suggestions'] = $this->formatSuggestions($response['suggestions']);
            }

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Arama yapılırken hata oluştu',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Otomatik tamamlama
     */
    public function autocomplete(Request $request): JsonResponse
    {
        $request->validate([
            'q' => 'required|string|min:2|max:100',
            'limit' => 'nullable|integer|min:1|max:20',
        ]);

        try {
            $query = $request->input('q');
            $limit = $request->input('limit', 10);

            // Analytics event kaydet
            $this->analyticsEngine->recordEvent('autocomplete_query', [
                'search_query' => $query,
            ]);

            $searchParams = [
                'query' => $query,
                'size' => $limit,
                'aggregations' => [
                    'categories' => [
                        'type' => 'terms',
                        'field' => 'category_name.keyword',
                        'size' => 5
                    ]
                ],
                'suggest' => [
                    'product_suggest' => [
                        'text' => $query,
                        'field' => 'name.autocomplete',
                        'size' => $limit
                    ]
                ]
            ];

            $response = $this->elasticsearch->search($searchParams);

            $result = [
                'query' => $query,
                'products' => array_slice($response['hits'], 0, $limit),
                'categories' => [],
                'suggestions' => [],
            ];

            // Kategori önerilerini formatla
            if (isset($response['aggregations']['categories']['buckets'])) {
                foreach ($response['aggregations']['categories']['buckets'] as $bucket) {
                    $result['categories'][] = [
                        'name' => $bucket['key'],
                        'count' => $bucket['doc_count']
                    ];
                }
            }

            // Önerileri formatla
            if (isset($response['suggestions']['product_suggest'])) {
                foreach ($response['suggestions']['product_suggest'] as $suggestion) {
                    foreach ($suggestion['options'] as $option) {
                        $result['suggestions'][] = [
                            'text' => $option['text'],
                            'score' => $option['_score']
                        ];
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Otomatik tamamlama yapılırken hata oluştu',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Arama önerileri
     */
    public function suggestions(Request $request): JsonResponse
    {
        $request->validate([
            'q' => 'required|string|min:2|max:100',
            'type' => 'nullable|string|in:spelling,completion,phrase',
        ]);

        try {
            $query = $request->input('q');
            $type = $request->input('type', 'completion');

            $searchParams = [
                'index' => config('elasticsearch.indices.products.name'),
                'body' => [
                    'suggest' => [
                        'text' => $query,
                        'product_completion' => [
                            'completion' => [
                                'field' => 'name.autocomplete',
                                'size' => 10,
                                'fuzzy' => [
                                    'fuzziness' => 'AUTO'
                                ]
                            ]
                        ]
                    ]
                ]
            ];

            $response = $this->elasticsearch->getClient()->search($searchParams);

            $suggestions = [];
            if (isset($response['suggest']['product_completion'])) {
                foreach ($response['suggest']['product_completion'] as $suggestion) {
                    foreach ($suggestion['options'] as $option) {
                        $suggestions[] = [
                            'text' => $option['text'],
                            'score' => $option['_score']
                        ];
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'query' => $query,
                    'type' => $type,
                    'suggestions' => $suggestions,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Öneriler alınırken hata oluştu',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Arama istatistikleri
     */
    public function searchStats(): JsonResponse
    {
        try {
            $stats = [
                'popular_searches' => $this->analyticsEngine->getPopularSearches(10),
                'trending_products' => $this->analyticsEngine->getTrendingProducts(10),
                'index_stats' => $this->indexManager->getIndexStatistics(),
                'cluster_health' => $this->indexManager->getClusterStatus(),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Arama istatistikleri alınamadı',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Filtreleri oluştur
     */
    protected function buildFilters(Request $request): array
    {
        $filters = [];

        if ($request->has('category_id')) {
            $filters['category_id'] = $request->input('category_id');
        }

        if ($request->has('category_ids')) {
            $filters['category_ids'] = $request->input('category_ids');
        }

        if ($request->has('brand')) {
            $filters['brand'] = $request->input('brand');
        }

        if ($request->has('brands')) {
            $filters['brands'] = $request->input('brands');
        }

        if ($request->has('price_min') || $request->has('price_max')) {
            $filters['price_range'] = [];
            if ($request->has('price_min')) {
                $filters['price_range']['min'] = $request->input('price_min');
            }
            if ($request->has('price_max')) {
                $filters['price_range']['max'] = $request->input('price_max');
            }
        }

        if ($request->has('in_stock') && $request->input('in_stock')) {
            $filters['in_stock'] = true;
        }

        // Aktif ürünleri filtrele
        $filters['status'] = 'active';

        return $filters;
    }

    /**
     * Sıralama oluştur
     */
    protected function buildSort(string $sort): array
    {
        switch ($sort) {
            case 'price_asc':
                return ['price' => 'asc'];
            case 'price_desc':
                return ['price' => 'desc'];
            case 'name_asc':
                return ['name' => 'asc'];
            case 'name_desc':
                return ['name' => 'desc'];
            case 'created_at_desc':
                return ['created_at' => 'desc'];
            case 'popularity':
                return ['popularity' => 'desc'];
            case 'sales':
                return ['sales' => 'desc'];
            case 'relevance':
            default:
                return ['relevance' => 'desc'];
        }
    }

    /**
     * Aggregations formatla
     */
    protected function formatAggregations(array $aggregations): array
    {
        $facets = [];

        foreach ($aggregations as $name => $aggregation) {
            if (isset($aggregation['buckets'])) {
                $facets[$name] = [];
                foreach ($aggregation['buckets'] as $bucket) {
                    $facets[$name][] = [
                        'key' => $bucket['key'],
                        'count' => $bucket['doc_count'],
                        'label' => $bucket['key_as_string'] ?? $bucket['key'],
                    ];
                }
            }
        }

        return $facets;
    }

    /**
     * Suggestions formatla
     */
    protected function formatSuggestions(array $suggestions): array
    {
        $formatted = [];

        foreach ($suggestions as $name => $suggestion) {
            $formatted[$name] = [];
            foreach ($suggestion as $item) {
                foreach ($item['options'] as $option) {
                    $formatted[$name][] = [
                        'text' => $option['text'],
                        'score' => $option['_score'],
                        'freq' => $option['freq'] ?? null,
                    ];
                }
            }
        }

        return $formatted;
    }
}
