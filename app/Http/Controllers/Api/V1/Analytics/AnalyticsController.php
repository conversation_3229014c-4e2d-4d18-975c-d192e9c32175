<?php

namespace App\Http\Controllers\Api\V1\Analytics;

use App\Http\Controllers\Controller;
use App\Core\Infrastructure\Analytics\AnalyticsEngine;
use App\Core\Infrastructure\Analytics\RealTimeProcessor;
use App\Core\Infrastructure\Search\SearchIndexManager;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

/**
 * AnalyticsController
 * Analytics API endpoints
 */
class AnalyticsController extends Controller
{
    protected AnalyticsEngine $analyticsEngine;
    protected RealTimeProcessor $realTimeProcessor;
    protected SearchIndexManager $indexManager;

    public function __construct(
        AnalyticsEngine $analyticsEngine,
        RealTimeProcessor $realTimeProcessor,
        SearchIndexManager $indexManager
    ) {
        $this->analyticsEngine = $analyticsEngine;
        $this->realTimeProcessor = $realTimeProcessor;
        $this->indexManager = $indexManager;
    }

    /**
     * Dashboard özet metrikleri
     */
    public function dashboard(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->input('start_date') ? 
            Carbon::parse($request->input('start_date')) : 
            now()->subDays(7);
            
        $endDate = $request->input('end_date') ? 
            Carbon::parse($request->input('end_date')) : 
            now();

        try {
            $summary = $this->analyticsEngine->getDashboardSummary($startDate, $endDate);
            
            return response()->json([
                'success' => true,
                'data' => $summary,
                'period' => [
                    'start_date' => $startDate->toDateString(),
                    'end_date' => $endDate->toDateString(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Dashboard verilerini alırken hata oluştu',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Gerçek zamanlı metrikleri al
     */
    public function realTime(): JsonResponse
    {
        try {
            $metrics = $this->realTimeProcessor->getRealTimeMetrics();
            
            return response()->json([
                'success' => true,
                'data' => $metrics,
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gerçek zamanlı veriler alınamadı',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Conversion funnel analizi
     */
    public function conversionFunnel(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        try {
            $startDate = Carbon::parse($request->input('start_date'));
            $endDate = Carbon::parse($request->input('end_date'));
            
            $funnel = $this->analyticsEngine->getConversionFunnel($startDate, $endDate);
            
            return response()->json([
                'success' => true,
                'data' => $funnel,
                'period' => [
                    'start_date' => $startDate->toDateString(),
                    'end_date' => $endDate->toDateString(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Conversion funnel analizi yapılamadı',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Ürün performans analizi
     */
    public function productPerformance(Request $request, int $productId): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        try {
            $startDate = Carbon::parse($request->input('start_date'));
            $endDate = Carbon::parse($request->input('end_date'));
            
            $performance = $this->analyticsEngine->getProductPerformance($productId, $startDate, $endDate);
            
            return response()->json([
                'success' => true,
                'data' => $performance,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ürün performans analizi yapılamadı',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Arama analitikleri
     */
    public function searchAnalytics(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        try {
            $startDate = Carbon::parse($request->input('start_date'));
            $endDate = Carbon::parse($request->input('end_date'));
            
            $analytics = $this->analyticsEngine->getSearchAnalytics($startDate, $endDate);
            
            return response()->json([
                'success' => true,
                'data' => $analytics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Arama analitikleri alınamadı',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Trend olan ürünleri al
     */
    public function trendingProducts(Request $request): JsonResponse
    {
        $request->validate([
            'limit' => 'nullable|integer|min:1|max:50',
        ]);

        try {
            $limit = $request->input('limit', 10);
            $trending = $this->analyticsEngine->getTrendingProducts($limit);
            
            return response()->json([
                'success' => true,
                'data' => $trending,
                'limit' => $limit,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Trend ürünler alınamadı',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Popüler arama terimlerini al
     */
    public function popularSearches(Request $request): JsonResponse
    {
        $request->validate([
            'limit' => 'nullable|integer|min:1|max:50',
        ]);

        try {
            $limit = $request->input('limit', 10);
            $searches = $this->analyticsEngine->getPopularSearches($limit);
            
            return response()->json([
                'success' => true,
                'data' => $searches,
                'limit' => $limit,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Popüler aramalar alınamadı',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Event kaydet
     */
    public function recordEvent(Request $request): JsonResponse
    {
        $request->validate([
            'event_type' => 'required|string|max:50',
            'data' => 'required|array',
        ]);

        try {
            $eventType = $request->input('event_type');
            $data = $request->input('data');
            
            // Kullanıcı bilgilerini ekle
            if (auth()->check()) {
                $data['user_id'] = auth()->id();
            }
            
            $success = $this->analyticsEngine->recordEvent($eventType, $data);
            
            if ($success) {
                // Real-time processing
                $this->realTimeProcessor->processEvent(array_merge($data, [
                    'event_type' => $eventType,
                    'timestamp' => now()->toISOString(),
                ]));
                
                return response()->json([
                    'success' => true,
                    'message' => 'Event başarıyla kaydedildi',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Event kaydedilemedi',
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Event kaydedilirken hata oluştu',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Son dakika metriklerini al
     */
    public function lastMinuteMetrics(): JsonResponse
    {
        try {
            $metrics = $this->realTimeProcessor->getLastMinuteMetrics();
            
            return response()->json([
                'success' => true,
                'data' => $metrics,
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Son dakika metrikleri alınamadı',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Index istatistiklerini al
     */
    public function indexStats(): JsonResponse
    {
        try {
            $stats = $this->indexManager->getIndexStatistics();
            $clusterStatus = $this->indexManager->getClusterStatus();
            
            return response()->json([
                'success' => true,
                'data' => [
                    'indices' => $stats,
                    'cluster' => $clusterStatus,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Index istatistikleri alınamadı',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Özel rapor oluştur
     */
    public function customReport(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'metrics' => 'required|array',
            'metrics.*' => 'string|in:conversion_funnel,search_analytics,product_performance,trending_products',
            'filters' => 'nullable|array',
        ]);

        try {
            $startDate = Carbon::parse($request->input('start_date'));
            $endDate = Carbon::parse($request->input('end_date'));
            $metrics = $request->input('metrics');
            $filters = $request->input('filters', []);

            $report = [
                'period' => [
                    'start_date' => $startDate->toDateString(),
                    'end_date' => $endDate->toDateString(),
                ],
                'filters' => $filters,
                'data' => [],
            ];

            foreach ($metrics as $metric) {
                switch ($metric) {
                    case 'conversion_funnel':
                        $report['data']['conversion_funnel'] = $this->analyticsEngine->getConversionFunnel($startDate, $endDate);
                        break;
                    case 'search_analytics':
                        $report['data']['search_analytics'] = $this->analyticsEngine->getSearchAnalytics($startDate, $endDate);
                        break;
                    case 'trending_products':
                        $report['data']['trending_products'] = $this->analyticsEngine->getTrendingProducts(20);
                        break;
                }
            }

            return response()->json([
                'success' => true,
                'data' => $report,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Özel rapor oluşturulamadı',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Analytics cache temizle
     */
    public function clearCache(Request $request): JsonResponse
    {
        $request->validate([
            'pattern' => 'nullable|string|max:100',
        ]);

        try {
            $pattern = $request->input('pattern');
            $clearedCount = $this->realTimeProcessor->clearCache($pattern);
            
            return response()->json([
                'success' => true,
                'message' => "Cache temizlendi: {$clearedCount} anahtar silindi",
                'cleared_count' => $clearedCount,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Cache temizlenemedi',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
