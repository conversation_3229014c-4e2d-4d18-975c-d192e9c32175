<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Core\Infrastructure\Analytics\AnalyticsEngine;
use App\Core\Infrastructure\Analytics\RealTimeProcessor;

/**
 * TrackPageViews
 * Page view tracking middleware
 */
class TrackPageViews
{
    protected AnalyticsEngine $analyticsEngine;
    protected RealTimeProcessor $realTimeProcessor;

    public function __construct(
        AnalyticsEngine $analyticsEngine,
        RealTimeProcessor $realTimeProcessor
    ) {
        $this->analyticsEngine = $analyticsEngine;
        $this->realTimeProcessor = $realTimeProcessor;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Sadece GET istekleri ve başarılı yanıtlar için track et
        if ($request->isMethod('GET') && $response->getStatusCode() === 200) {
            $this->trackPageView($request);
        }

        return $response;
    }

    /**
     * Sayfa görüntülenmesini track et
     */
    protected function trackPageView(Request $request): void
    {
        try {
            // Bot ve crawler'ları filtrele
            if ($this->isBot($request)) {
                return;
            }

            // API isteklerini filtrele
            if ($request->is('api/*')) {
                return;
            }

            // Admin paneli isteklerini filtrele (isteğe bağlı)
            if ($request->is('admin/*')) {
                return;
            }

            $eventData = [
                'url' => $request->fullUrl(),
                'path' => $request->path(),
                'method' => $request->method(),
                'user_id' => auth()->id(),
                'referrer' => $request->header('referer'),
                'user_agent' => $request->userAgent(),
                'ip_address' => $request->ip(),
            ];

            // Ürün sayfası kontrolü
            if ($this->isProductPage($request)) {
                $productId = $this->extractProductId($request);
                if ($productId) {
                    $eventData['product_id'] = $productId;
                    $eventData['page_type'] = 'product';
                }
            }

            // Kategori sayfası kontrolü
            if ($this->isCategoryPage($request)) {
                $categoryId = $this->extractCategoryId($request);
                if ($categoryId) {
                    $eventData['category_id'] = $categoryId;
                    $eventData['page_type'] = 'category';
                }
            }

            // Arama sayfası kontrolü
            if ($this->isSearchPage($request)) {
                $searchQuery = $request->input('q');
                if ($searchQuery) {
                    $eventData['search_query'] = $searchQuery;
                    $eventData['page_type'] = 'search';
                }
            }

            // Ana sayfa kontrolü
            if ($request->path() === '/') {
                $eventData['page_type'] = 'home';
            }

            // Event kaydet
            $this->analyticsEngine->recordEvent('page_view', $eventData);
            
            // Real-time processing
            $this->realTimeProcessor->processEvent(array_merge($eventData, [
                'event_type' => 'page_view',
                'timestamp' => now()->toISOString(),
            ]));

        } catch (\Exception $e) {
            // Hata durumunda sessizce devam et
            \Log::error('Page view tracking failed: ' . $e->getMessage());
        }
    }

    /**
     * Bot kontrolü
     */
    protected function isBot(Request $request): bool
    {
        $userAgent = strtolower($request->userAgent() ?? '');
        
        $botPatterns = [
            'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget',
            'googlebot', 'bingbot', 'slurp', 'duckduckbot',
            'baiduspider', 'yandexbot', 'facebookexternalhit',
            'twitterbot', 'linkedinbot', 'whatsapp', 'telegram',
        ];

        foreach ($botPatterns as $pattern) {
            if (str_contains($userAgent, $pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Ürün sayfası kontrolü
     */
    protected function isProductPage(Request $request): bool
    {
        return $request->is('products/*') || $request->is('urun/*');
    }

    /**
     * Ürün ID'sini çıkar
     */
    protected function extractProductId(Request $request): ?int
    {
        $path = $request->path();
        
        // /products/{id} formatı
        if (preg_match('/products\/(\d+)/', $path, $matches)) {
            return (int) $matches[1];
        }
        
        // /urun/{slug} formatı - slug'dan ID bul
        if (preg_match('/urun\/([^\/]+)/', $path, $matches)) {
            $slug = $matches[1];
            $product = \App\Models\Product::where('slug', $slug)->first();
            return $product ? $product->id : null;
        }

        return null;
    }

    /**
     * Kategori sayfası kontrolü
     */
    protected function isCategoryPage(Request $request): bool
    {
        return $request->is('categories/*') || $request->is('kategori/*');
    }

    /**
     * Kategori ID'sini çıkar
     */
    protected function extractCategoryId(Request $request): ?int
    {
        $path = $request->path();
        
        // /categories/{id} formatı
        if (preg_match('/categories\/(\d+)/', $path, $matches)) {
            return (int) $matches[1];
        }
        
        // /kategori/{slug} formatı - slug'dan ID bul
        if (preg_match('/kategori\/([^\/]+)/', $path, $matches)) {
            $slug = $matches[1];
            $category = \App\Models\Category::where('slug', $slug)->first();
            return $category ? $category->id : null;
        }

        return null;
    }

    /**
     * Arama sayfası kontrolü
     */
    protected function isSearchPage(Request $request): bool
    {
        return $request->is('search') || $request->is('arama');
    }
}
