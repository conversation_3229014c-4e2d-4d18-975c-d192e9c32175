<?php

namespace App\Console\Commands\Search;

use Illuminate\Console\Command;
use App\Core\Infrastructure\Search\ElasticsearchService;
use App\Core\Infrastructure\Search\SearchIndexManager;

/**
 * ElasticsearchIndexCommand
 * Elasticsearch index management command
 */
class ElasticsearchIndexCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'elasticsearch:index 
                            {action : Action to perform (create, rebuild, sync, delete, stats)}
                            {--index= : Specific index name}
                            {--force : Force action without confirmation}';

    /**
     * The console command description.
     */
    protected $description = 'Elasticsearch index management operations';

    protected ElasticsearchService $elasticsearch;
    protected SearchIndexManager $indexManager;

    public function __construct(
        ElasticsearchService $elasticsearch,
        SearchIndexManager $indexManager
    ) {
        parent::__construct();
        $this->elasticsearch = $elasticsearch;
        $this->indexManager = $indexManager;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');
        $indexName = $this->option('index');
        $force = $this->option('force');

        try {
            switch ($action) {
                case 'create':
                    return $this->createIndices($indexName, $force);
                case 'rebuild':
                    return $this->rebuildIndex($indexName, $force);
                case 'sync':
                    return $this->syncIndex($indexName);
                case 'delete':
                    return $this->deleteIndex($indexName, $force);
                case 'stats':
                    return $this->showStats($indexName);
                default:
                    $this->error("Unknown action: {$action}");
                    $this->showHelp();
                    return 1;
            }
        } catch (\Exception $e) {
            $this->error("Command failed: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * Index oluştur
     */
    protected function createIndices(?string $indexName, bool $force): int
    {
        $this->info('Creating Elasticsearch indices...');

        if ($indexName) {
            // Belirli bir index oluştur
            if (!$force && !$this->confirm("Create index '{$indexName}'?")) {
                $this->info('Operation cancelled.');
                return 0;
            }

            $this->info("Creating index: {$indexName}");
            
            if ($this->elasticsearch->createOrUpdateIndex($indexName)) {
                $this->info("✓ Index '{$indexName}' created successfully");
                return 0;
            } else {
                $this->error("✗ Failed to create index '{$indexName}'");
                return 1;
            }
        } else {
            // Tüm indexleri oluştur
            if (!$force && !$this->confirm('Create all indices?')) {
                $this->info('Operation cancelled.');
                return 0;
            }

            if ($this->indexManager->createAllIndices()) {
                $this->info('✓ All indices created successfully');
                return 0;
            } else {
                $this->error('✗ Some indices failed to create');
                return 1;
            }
        }
    }

    /**
     * Index yeniden oluştur
     */
    protected function rebuildIndex(?string $indexName, bool $force): int
    {
        if (!$indexName) {
            $this->error('Index name is required for rebuild operation');
            return 1;
        }

        if (!$force && !$this->confirm("Rebuild index '{$indexName}'? This will delete all existing data!")) {
            $this->info('Operation cancelled.');
            return 0;
        }

        $this->info("Rebuilding index: {$indexName}");
        
        $progressBar = $this->output->createProgressBar(3);
        $progressBar->setFormat('verbose');

        // 1. Index sil
        $progressBar->setMessage('Deleting old index...');
        $progressBar->advance();

        // 2. Yeni index oluştur
        $progressBar->setMessage('Creating new index...');
        $progressBar->advance();

        // 3. Verileri yeniden indexle
        $progressBar->setMessage('Reindexing data...');
        $progressBar->advance();

        $progressBar->finish();
        $this->newLine();

        if ($this->indexManager->rebuildIndex($indexName)) {
            $this->info("✓ Index '{$indexName}' rebuilt successfully");
            return 0;
        } else {
            $this->error("✗ Failed to rebuild index '{$indexName}'");
            return 1;
        }
    }

    /**
     * Index senkronize et
     */
    protected function syncIndex(?string $indexName): int
    {
        if ($indexName && $indexName !== 'products') {
            $this->error('Sync operation is only available for products index');
            return 1;
        }

        $this->info('Synchronizing product index...');
        
        $progressBar = $this->output->createProgressBar();
        $progressBar->setFormat('verbose');
        $progressBar->setMessage('Starting synchronization...');
        $progressBar->start();

        $stats = $this->indexManager->syncProductIndex();

        $progressBar->finish();
        $this->newLine();

        // Sonuçları göster
        $this->info('Synchronization completed:');
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Products', $stats['total_products']],
                ['Indexed Products', $stats['indexed_products']],
                ['Failed Products', $stats['failed_products']],
                ['Duration', $stats['duration'] . ' seconds'],
            ]
        );

        if ($stats['failed_products'] > 0) {
            $this->warn("Warning: {$stats['failed_products']} products failed to index");
            return 1;
        }

        $this->info('✓ Index synchronized successfully');
        return 0;
    }

    /**
     * Index sil
     */
    protected function deleteIndex(?string $indexName, bool $force): int
    {
        if (!$indexName) {
            $this->error('Index name is required for delete operation');
            return 1;
        }

        if (!$force && !$this->confirm("Delete index '{$indexName}'? This action cannot be undone!")) {
            $this->info('Operation cancelled.');
            return 0;
        }

        $this->info("Deleting index: {$indexName}");
        
        $indexRealName = config("elasticsearch.indices.{$indexName}.name");
        if (!$indexRealName) {
            $this->error("Index configuration not found: {$indexName}");
            return 1;
        }

        if ($this->elasticsearch->deleteIndex($indexRealName)) {
            $this->info("✓ Index '{$indexName}' deleted successfully");
            return 0;
        } else {
            $this->error("✗ Failed to delete index '{$indexName}'");
            return 1;
        }
    }

    /**
     * İstatistikleri göster
     */
    protected function showStats(?string $indexName): int
    {
        $this->info('Elasticsearch Statistics');
        $this->newLine();

        // Cluster durumu
        $clusterStatus = $this->indexManager->getClusterStatus();
        $this->info('Cluster Health:');
        $this->table(
            ['Metric', 'Value'],
            [
                ['Status', $clusterStatus['status']],
                ['Cluster Name', $clusterStatus['cluster_name']],
                ['Nodes', $clusterStatus['number_of_nodes']],
                ['Data Nodes', $clusterStatus['number_of_data_nodes']],
                ['Active Shards', $clusterStatus['active_shards']],
                ['Unassigned Shards', $clusterStatus['unassigned_shards']],
            ]
        );

        $this->newLine();

        // Index istatistikleri
        $indexStats = $this->indexManager->getIndexStatistics();
        
        if ($indexName) {
            if (isset($indexStats[$indexName])) {
                $this->info("Index Statistics: {$indexName}");
                $stats = $indexStats[$indexName];
                $this->table(
                    ['Metric', 'Value'],
                    [
                        ['Name', $stats['name']],
                        ['Document Count', number_format($stats['document_count'])],
                        ['Size', $stats['size_human']],
                    ]
                );
            } else {
                $this->error("Index not found: {$indexName}");
                return 1;
            }
        } else {
            $this->info('All Indices:');
            $tableData = [];
            foreach ($indexStats as $name => $stats) {
                $tableData[] = [
                    $name,
                    $stats['name'],
                    number_format($stats['document_count']),
                    $stats['size_human'],
                ];
            }
            
            $this->table(
                ['Index', 'Real Name', 'Documents', 'Size'],
                $tableData
            );
        }

        return 0;
    }

    /**
     * Yardım göster
     */
    protected function showHelp(): void
    {
        $this->info('Available actions:');
        $this->line('  create   - Create indices');
        $this->line('  rebuild  - Rebuild an index (deletes and recreates)');
        $this->line('  sync     - Synchronize product index with database');
        $this->line('  delete   - Delete an index');
        $this->line('  stats    - Show index statistics');
        $this->newLine();
        $this->info('Examples:');
        $this->line('  php artisan elasticsearch:index create');
        $this->line('  php artisan elasticsearch:index create --index=products');
        $this->line('  php artisan elasticsearch:index rebuild --index=products --force');
        $this->line('  php artisan elasticsearch:index sync');
        $this->line('  php artisan elasticsearch:index stats --index=products');
    }
}
