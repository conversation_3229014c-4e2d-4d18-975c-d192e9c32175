<?php

namespace App\Console\Commands\Queue;

use App\Core\Infrastructure\Queue\Services\QueueHealthMonitor;
use Illuminate\Console\Command;

/**
 * Queue Health Check Command
 * Queue sağlık durumu kontrol komutu
 */
class QueueHealthCheckCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'queue:health-check 
                            {--detailed : Detaylı sağlık raporu göster}
                            {--json : JSON formatında çıktı ver}
                            {--watch : Canlı izleme modu}
                            {--refresh=30 : Watch modunda yenileme süresi (saniye)}
                            {--alert : Kritik durumda alert gönder}
                            {--export= : Raporu dosyaya export et}';

    /**
     * The console command description.
     */
    protected $description = 'Queue sisteminin sağlık durumunu kontrol eder';

    protected QueueHealthMonitor $healthMonitor;

    /**
     * Create a new command instance.
     */
    public function __construct(QueueHealthMonitor $healthMonitor)
    {
        parent::__construct();
        $this->healthMonitor = $healthMonitor;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        if ($this->option('watch')) {
            return $this->handleWatchMode();
        }

        $healthReport = $this->healthMonitor->checkOverallHealth();

        if ($this->option('json')) {
            $this->line(json_encode($healthReport, JSON_PRETTY_PRINT));
            return Command::SUCCESS;
        }

        $this->displayHealthReport($healthReport);

        if ($this->option('export')) {
            $this->exportReport($healthReport, $this->option('export'));
        }

        // Exit code'u sağlık durumuna göre belirle
        return match($healthReport['overall_status']) {
            'healthy' => Command::SUCCESS,
            'warning' => 1,
            'critical', 'error' => 2,
            default => Command::SUCCESS,
        };
    }

    /**
     * Watch modu
     */
    protected function handleWatchMode(): int
    {
        $refresh = (int) $this->option('refresh');
        $this->info("🔍 Queue Health Monitor - Canlı İzleme Modu");
        $this->info("Her {$refresh} saniyede yenilenir. Çıkmak için Ctrl+C tuşlayın.");
        $this->newLine();

        while (true) {
            // Ekranı temizle
            if (PHP_OS_FAMILY === 'Windows') {
                system('cls');
            } else {
                system('clear');
            }

            $this->info("🔄 Queue Health Status - " . now()->format('Y-m-d H:i:s'));
            $this->line(str_repeat('=', 70));

            try {
                $healthReport = $this->healthMonitor->checkOverallHealth();
                $this->displayCompactHealthReport($healthReport);
            } catch (\Exception $e) {
                $this->error("❌ Sağlık kontrolü hatası: " . $e->getMessage());
            }

            sleep($refresh);
        }

        return Command::SUCCESS;
    }

    /**
     * Sağlık raporunu göster
     */
    protected function displayHealthReport(array $report): void
    {
        $this->info("🏥 Queue System Health Report");
        $this->info("Timestamp: " . $report['timestamp']);
        $this->newLine();

        // Genel durum
        $statusColor = $this->getStatusColor($report['overall_status']);
        $this->$statusColor("Overall Status: " . strtoupper($report['overall_status']));
        $this->newLine();

        // Özet
        if (isset($report['summary'])) {
            $this->displaySummary($report['summary']);
        }

        // Detaylı kontroller
        if ($this->option('detailed')) {
            $this->displayDetailedChecks($report['checks']);
        } else {
            $this->displayBasicChecks($report['checks']);
        }

        // Öneriler
        if (!empty($report['recommendations'])) {
            $this->displayRecommendations($report['recommendations']);
        }
    }

    /**
     * Kompakt sağlık raporu (watch modu için)
     */
    protected function displayCompactHealthReport(array $report): void
    {
        $statusColor = $this->getStatusColor($report['overall_status']);
        $this->$statusColor("Status: " . strtoupper($report['overall_status']));
        $this->newLine();

        // Kritik metrikler
        $checks = $report['checks'];
        
        // Queue sizes
        if (isset($checks['queue_sizes']['queues'])) {
            $this->info("📊 Queue Sizes:");
            foreach ($checks['queue_sizes']['queues'] as $queue => $data) {
                $color = $this->getStatusColor($data['status']);
                $this->$color("  {$queue}: {$data['size']} jobs ({$data['utilization']}%)");
            }
            $this->newLine();
        }

        // Worker status
        if (isset($checks['worker_status'])) {
            $workerStatus = $checks['worker_status'];
            $color = $this->getStatusColor($workerStatus['status']);
            $this->$color("👷 Workers: {$workerStatus['worker_count']} active");
            $this->newLine();
        }

        // Failed jobs
        if (isset($checks['failed_jobs'])) {
            $failedJobs = $checks['failed_jobs'];
            $color = $this->getStatusColor($failedJobs['status']);
            $this->$color("❌ Failed Jobs: {$failedJobs['total_failed']} total, {$failedJobs['recent_failures']} recent");
            $this->newLine();
        }

        // Processing rate
        if (isset($checks['job_processing_rate'])) {
            $processingRate = $checks['job_processing_rate'];
            $color = $this->getStatusColor($processingRate['status']);
            $this->$color("⚡ Processing Rate: {$processingRate['current_rate']} jobs/min");
            $this->newLine();
        }

        // Alerts
        $allAlerts = [];
        foreach ($checks as $check) {
            if (isset($check['alerts'])) {
                $allAlerts = array_merge($allAlerts, $check['alerts']);
            }
        }

        if (!empty($allAlerts)) {
            $this->warn("⚠️  Active Alerts:");
            foreach (array_slice($allAlerts, 0, 5) as $alert) {
                $this->line("  • " . $alert);
            }
            if (count($allAlerts) > 5) {
                $this->line("  • ... ve " . (count($allAlerts) - 5) . " alert daha");
            }
        }
    }

    /**
     * Özet göster
     */
    protected function displaySummary(array $summary): void
    {
        $this->info("📈 Summary:");
        $this->table(
            ['Status', 'Count'],
            [
                ['Healthy', $summary['healthy_checks']],
                ['Warning', $summary['warning_checks']],
                ['Critical', $summary['critical_checks']],
                ['Error', $summary['error_checks']],
            ]
        );
        $this->newLine();
    }

    /**
     * Temel kontrolleri göster
     */
    protected function displayBasicChecks(array $checks): void
    {
        $this->info("🔍 Health Checks:");
        
        $tableData = [];
        foreach ($checks as $checkName => $check) {
            $status = $check['status'];
            $statusIcon = $this->getStatusIcon($status);
            
            $tableData[] = [
                $checkName,
                $statusIcon . ' ' . strtoupper($status),
                $this->getCheckSummary($checkName, $check),
            ];
        }

        $this->table(['Check', 'Status', 'Summary'], $tableData);
        $this->newLine();
    }

    /**
     * Detaylı kontrolleri göster
     */
    protected function displayDetailedChecks(array $checks): void
    {
        $this->info("🔍 Detailed Health Checks:");
        $this->newLine();

        foreach ($checks as $checkName => $check) {
            $statusColor = $this->getStatusColor($check['status']);
            $statusIcon = $this->getStatusIcon($check['status']);
            
            $this->$statusColor("{$statusIcon} " . ucwords(str_replace('_', ' ', $checkName)));
            
            // Check-specific details
            $this->displayCheckDetails($checkName, $check);
            $this->newLine();
        }
    }

    /**
     * Check detaylarını göster
     */
    protected function displayCheckDetails(string $checkName, array $check): void
    {
        switch ($checkName) {
            case 'queue_sizes':
                if (isset($check['queues'])) {
                    foreach ($check['queues'] as $queue => $data) {
                        $this->line("  {$queue}: {$data['size']}/{$data['threshold']} ({$data['utilization']}%)");
                    }
                }
                break;
                
            case 'worker_status':
                $this->line("  Active workers: " . ($check['worker_count'] ?? 0));
                if (isset($check['horizon']['status'])) {
                    $this->line("  Horizon status: " . $check['horizon']['status']);
                }
                break;
                
            case 'failed_jobs':
                $this->line("  Total failed: " . ($check['total_failed'] ?? 0));
                $this->line("  Recent failures: " . ($check['recent_failures'] ?? 0));
                $this->line("  Failure rate: " . ($check['failure_rate'] ?? 0) . "%");
                break;
                
            case 'memory_usage':
                $this->line("  Current usage: " . ($check['formatted_usage'] ?? 'N/A'));
                $this->line("  Memory limit: " . ($check['formatted_limit'] ?? 'N/A'));
                $this->line("  Usage percent: " . ($check['usage_percent'] ?? 0) . "%");
                break;
        }

        // Alerts göster
        if (!empty($check['alerts'])) {
            foreach ($check['alerts'] as $alert) {
                $this->warn("    ⚠️  " . $alert);
            }
        }
    }

    /**
     * Önerileri göster
     */
    protected function displayRecommendations(array $recommendations): void
    {
        $this->warn("💡 Recommendations:");
        foreach ($recommendations as $recommendation) {
            $this->line("  • " . $recommendation);
        }
        $this->newLine();
    }

    /**
     * Raporu export et
     */
    protected function exportReport(array $report, string $path): void
    {
        $directory = dirname($path);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }

        $extension = pathinfo($path, PATHINFO_EXTENSION);
        
        switch ($extension) {
            case 'json':
                file_put_contents($path, json_encode($report, JSON_PRETTY_PRINT));
                break;
            default:
                file_put_contents($path, $this->formatReportAsText($report));
                break;
        }

        $this->info("📄 Rapor export edildi: {$path}");
    }

    /**
     * Helper methods
     */
    protected function getStatusColor(string $status): string
    {
        return match($status) {
            'healthy' => 'info',
            'warning' => 'warn',
            'critical', 'error' => 'error',
            default => 'line',
        };
    }

    protected function getStatusIcon(string $status): string
    {
        return match($status) {
            'healthy' => '✅',
            'warning' => '⚠️',
            'critical' => '🔴',
            'error' => '❌',
            default => '❓',
        };
    }

    protected function getCheckSummary(string $checkName, array $check): string
    {
        return match($checkName) {
            'queue_sizes' => ($check['total_pending_jobs'] ?? 0) . ' total pending jobs',
            'worker_status' => ($check['worker_count'] ?? 0) . ' active workers',
            'failed_jobs' => ($check['total_failed'] ?? 0) . ' failed jobs',
            'memory_usage' => ($check['usage_percent'] ?? 0) . '% memory used',
            default => 'OK',
        };
    }

    protected function formatReportAsText(array $report): string
    {
        $text = "Queue Health Report\n";
        $text .= "Generated: " . $report['timestamp'] . "\n";
        $text .= "Overall Status: " . strtoupper($report['overall_status']) . "\n";
        $text .= str_repeat('=', 50) . "\n\n";

        foreach ($report['checks'] as $checkName => $check) {
            $text .= ucwords(str_replace('_', ' ', $checkName)) . ": " . strtoupper($check['status']) . "\n";
        }

        return $text;
    }
}
