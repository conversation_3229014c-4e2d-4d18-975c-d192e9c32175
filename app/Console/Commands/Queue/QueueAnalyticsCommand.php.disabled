<?php

namespace App\Console\Commands\Queue;

use App\Core\Infrastructure\Queue\Contracts\QueueAnalyticsInterface;
use App\Core\Infrastructure\Queue\Contracts\QueueMonitoringInterface;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

/**
 * Queue Analytics Command
 * Queue analitik raporları ve metrikleri
 */
class QueueAnalyticsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'queue:analytics 
                            {--type=summary : <PERSON><PERSON> tür<PERSON> (summary, detailed, performance, health, trends)}
                            {--queue= : Belirli queue için analiz}
                            {--job-class= : Belirli job class için analiz}
                            {--period=last_24_hours : <PERSON><PERSON><PERSON> periyodu (last_hour, last_24_hours, last_7_days, last_30_days)}
                            {--format=table : Çıktı formatı (table, json, csv)}
                            {--export= : Raporu dosyaya export et}
                            {--watch : Real-time monitoring modu}
                            {--refresh=30 : Watch modunda refresh interval (saniye)}';

    /**
     * The console command description.
     */
    protected $description = 'Queue analitik raporları ve performans metrikleri';

    protected QueueAnalyticsInterface $analytics;
    protected QueueMonitoringInterface $monitoring;

    /**
     * Create a new command instance.
     */
    public function __construct(QueueAnalyticsInterface $analytics, QueueMonitoringInterface $monitoring)
    {
        parent::__construct();
        $this->analytics = $analytics;
        $this->monitoring = $monitoring;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $type = $this->option('type');
        $format = $this->option('format');
        $watch = $this->option('watch');

        if ($watch) {
            return $this->handleWatchMode();
        }

        $this->info("🔍 Queue Analytics Raporu Oluşturuluyor...");
        $this->newLine();

        try {
            $report = $this->generateReport($type);
            $this->displayReport($report, $format);

            // Export işlemi
            if ($exportPath = $this->option('export')) {
                $this->exportReport($report, $exportPath, $format);
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Rapor oluşturma hatası: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Watch mode - real-time monitoring
     */
    protected function handleWatchMode(): int
    {
        $refreshInterval = (int) $this->option('refresh');
        
        $this->info("🔄 Real-time Queue Monitoring başlatılıyor...");
        $this->info("📊 Refresh interval: {$refreshInterval} saniye");
        $this->info("⏹️  Durdurmak için Ctrl+C");
        $this->newLine();

        while (true) {
            // Ekranı temizle
            $this->output->write("\033[2J\033[H");
            
            // Timestamp göster
            $this->info("🕐 " . now()->format('Y-m-d H:i:s'));
            $this->newLine();

            try {
                // Health check
                $health = $this->monitoring->performHealthCheck();
                $this->displayHealthStatus($health);

                // Quick stats
                $stats = $this->analytics->getQueueStatistics($this->option('queue'));
                $this->displayQuickStats($stats);

                $this->newLine();
                $this->line("Sonraki güncelleme: " . now()->addSeconds($refreshInterval)->format('H:i:s'));

            } catch (\Exception $e) {
                $this->error("❌ Monitoring hatası: " . $e->getMessage());
            }

            sleep($refreshInterval);
        }

        return Command::SUCCESS;
    }

    /**
     * Rapor oluştur
     */
    protected function generateReport(string $type): array
    {
        $queue = $this->option('queue');
        $jobClass = $this->option('job-class');
        $period = $this->option('period');

        return match ($type) {
            'summary' => $this->generateSummaryReport($queue, $period),
            'detailed' => $this->generateDetailedReport($queue, $jobClass, $period),
            'performance' => $this->generatePerformanceReport($queue, $jobClass, $period),
            'health' => $this->generateHealthReport(),
            'trends' => $this->generateTrendsReport($queue, $period),
            default => $this->generateSummaryReport($queue, $period),
        };
    }

    /**
     * Özet rapor oluştur
     */
    protected function generateSummaryReport(string $queue = null, string $period = 'last_24_hours'): array
    {
        $stats = $this->analytics->getQueueStatistics($queue);
        $throughput = $this->analytics->getThroughputAnalysis($queue, $period);
        $health = $this->monitoring->performHealthCheck();

        return [
            'type' => 'summary',
            'queue' => $queue,
            'period' => $period,
            'timestamp' => now(),
            'overview' => [
                'total_jobs' => $stats['total_jobs'],
                'pending_jobs' => $stats['pending_jobs'],
                'completed_jobs' => $stats['completed_jobs'],
                'failed_jobs' => $stats['failed_jobs'],
                'success_rate' => round($stats['success_rate'] * 100, 2) . '%',
                'avg_processing_time' => round($stats['avg_processing_time'], 2) . 's',
                'throughput_per_minute' => round($stats['throughput_per_minute'], 2),
            ],
            'health_status' => $health['overall_status'],
            'active_alerts' => count($health['checks']),
            'throughput_summary' => $throughput['summary'],
        ];
    }

    /**
     * Detaylı rapor oluştur
     */
    protected function generateDetailedReport(string $queue = null, string $jobClass = null, string $period = 'last_24_hours'): array
    {
        $stats = $this->analytics->getQueueStatistics($queue);
        $performance = $this->analytics->getJobPerformanceMetrics($jobClass, ['period' => $period]);
        $throughput = $this->analytics->getThroughputAnalysis($queue, $period);

        return [
            'type' => 'detailed',
            'queue' => $queue,
            'job_class' => $jobClass,
            'period' => $period,
            'timestamp' => now(),
            'statistics' => $stats,
            'performance_metrics' => $performance,
            'throughput_analysis' => $throughput,
            'queue_breakdown' => $stats['queues'],
        ];
    }

    /**
     * Performans raporu oluştur
     */
    protected function generatePerformanceReport(string $queue = null, string $jobClass = null, string $period = 'last_24_hours'): array
    {
        $performance = $this->analytics->getJobPerformanceMetrics($jobClass, ['period' => $period]);
        $throughput = $this->analytics->getThroughputAnalysis($queue, $period);

        return [
            'type' => 'performance',
            'queue' => $queue,
            'job_class' => $jobClass,
            'period' => $period,
            'timestamp' => now(),
            'execution_metrics' => [
                'total_executions' => $performance['total_executions'],
                'avg_execution_time' => round($performance['avg_execution_time'], 3),
                'min_execution_time' => round($performance['min_execution_time'], 3),
                'max_execution_time' => round($performance['max_execution_time'], 3),
                'avg_memory_usage_mb' => round($performance['avg_memory_usage'] / 1024 / 1024, 2),
            ],
            'success_metrics' => [
                'successful_executions' => $performance['successful_executions'],
                'failed_executions' => $performance['failed_executions'],
                'retry_rate' => round($performance['retry_rate'] * 100, 2) . '%',
            ],
            'throughput_metrics' => $throughput['summary'],
            'error_distribution' => $performance['error_distribution'],
            'hourly_distribution' => $performance['hourly_distribution'],
        ];
    }

    /**
     * Sağlık raporu oluştur
     */
    protected function generateHealthReport(): array
    {
        $health = $this->monitoring->performHealthCheck();
        $alertHistory = $this->monitoring->getAlertHistory(['limit' => 50]);

        return [
            'type' => 'health',
            'timestamp' => now(),
            'health_status' => $health,
            'recent_alerts' => array_slice($alertHistory, 0, 10),
            'alert_summary' => [
                'total_alerts' => count($alertHistory),
                'critical_alerts' => count(array_filter($alertHistory, fn($a) => $a->severity === 'critical')),
                'warning_alerts' => count(array_filter($alertHistory, fn($a) => $a->severity === 'warning')),
                'resolved_alerts' => count(array_filter($alertHistory, fn($a) => $a->status === 'resolved')),
            ],
        ];
    }

    /**
     * Trend raporu oluştur
     */
    protected function generateTrendsReport(string $queue = null, string $period = 'last_7_days'): array
    {
        $trends = $this->analytics->getQueueTrends($queue, $period);

        return [
            'type' => 'trends',
            'queue' => $queue,
            'period' => $period,
            'timestamp' => now(),
            'trends' => $trends,
        ];
    }

    /**
     * Raporu görüntüle
     */
    protected function displayReport(array $report, string $format): void
    {
        switch ($format) {
            case 'json':
                $this->line(json_encode($report, JSON_PRETTY_PRINT));
                break;
            
            case 'csv':
                $this->displayCsvReport($report);
                break;
            
            case 'table':
            default:
                $this->displayTableReport($report);
                break;
        }
    }

    /**
     * Tablo formatında rapor göster
     */
    protected function displayTableReport(array $report): void
    {
        $this->info("📊 Queue Analytics Raporu - " . ucfirst($report['type']));
        $this->newLine();

        switch ($report['type']) {
            case 'summary':
                $this->displaySummaryTable($report);
                break;
            
            case 'detailed':
                $this->displayDetailedTable($report);
                break;
            
            case 'performance':
                $this->displayPerformanceTable($report);
                break;
            
            case 'health':
                $this->displayHealthTable($report);
                break;
            
            case 'trends':
                $this->displayTrendsTable($report);
                break;
        }
    }

    /**
     * Özet tablosu göster
     */
    protected function displaySummaryTable(array $report): void
    {
        $this->table(
            ['Metrik', 'Değer'],
            [
                ['Toplam Job', number_format($report['overview']['total_jobs'])],
                ['Bekleyen Job', number_format($report['overview']['pending_jobs'])],
                ['Tamamlanan Job', number_format($report['overview']['completed_jobs'])],
                ['Başarısız Job', number_format($report['overview']['failed_jobs'])],
                ['Başarı Oranı', $report['overview']['success_rate']],
                ['Ort. İşlem Süresi', $report['overview']['avg_processing_time']],
                ['Dakika/Job', $report['overview']['throughput_per_minute']],
                ['Sağlık Durumu', $report['health_status']],
            ]
        );
    }

    /**
     * Sağlık durumunu göster
     */
    protected function displayHealthStatus(array $health): void
    {
        $statusIcon = match ($health['overall_status']) {
            'healthy' => '✅',
            'warning' => '⚠️',
            'critical' => '❌',
            default => '❓',
        };

        $this->line("{$statusIcon} Genel Durum: " . strtoupper($health['overall_status']));
        
        if (!empty($health['checks'])) {
            $this->newLine();
            foreach ($health['checks'] as $checkName => $result) {
                $icon = match ($result['status']) {
                    'healthy' => '✅',
                    'warning' => '⚠️',
                    'critical' => '❌',
                    default => '❓',
                };
                $this->line("  {$icon} " . ucfirst(str_replace('_', ' ', $checkName)) . ": {$result['message']}");
            }
        }
    }

    /**
     * Hızlı istatistikleri göster
     */
    protected function displayQuickStats(array $stats): void
    {
        $this->newLine();
        $this->info("📈 Hızlı İstatistikler:");
        $this->line("  📦 Bekleyen: " . number_format($stats['pending_jobs']));
        $this->line("  ✅ Tamamlanan: " . number_format($stats['completed_jobs']));
        $this->line("  ❌ Başarısız: " . number_format($stats['failed_jobs']));
        $this->line("  📊 Başarı Oranı: " . round($stats['success_rate'] * 100, 1) . "%");
        $this->line("  ⏱️  Ort. Süre: " . round($stats['avg_processing_time'], 2) . "s");
    }

    /**
     * Raporu export et
     */
    protected function exportReport(array $report, string $path, string $format): void
    {
        try {
            $content = match ($format) {
                'json' => json_encode($report, JSON_PRETTY_PRINT),
                'csv' => $this->convertToCsv($report),
                default => json_encode($report, JSON_PRETTY_PRINT),
            };

            file_put_contents($path, $content);
            $this->info("✅ Rapor export edildi: {$path}");

        } catch (\Exception $e) {
            $this->error("❌ Export hatası: " . $e->getMessage());
        }
    }

    /**
     * CSV formatına dönüştür
     */
    protected function convertToCsv(array $report): string
    {
        // Basit CSV dönüştürme implementasyonu
        $csv = "Type,Timestamp,Queue,Period\n";
        $csv .= "{$report['type']},{$report['timestamp']},{$report['queue']},{$report['period']}\n\n";
        
        if (isset($report['overview'])) {
            $csv .= "Metric,Value\n";
            foreach ($report['overview'] as $key => $value) {
                $csv .= "{$key},{$value}\n";
            }
        }

        return $csv;
    }

    // Diğer display metodları burada implement edilebilir...
    protected function displayDetailedTable(array $report): void
    {
        // Detailed table implementation
    }

    protected function displayPerformanceTable(array $report): void
    {
        // Performance table implementation
    }

    protected function displayHealthTable(array $report): void
    {
        // Health table implementation
    }

    protected function displayTrendsTable(array $report): void
    {
        // Trends table implementation
    }

    protected function displayCsvReport(array $report): void
    {
        $this->line($this->convertToCsv($report));
    }
}
