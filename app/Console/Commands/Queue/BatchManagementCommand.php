<?php

namespace App\Console\Commands\Queue;

use App\Core\Infrastructure\Queue\Services\BatchJobManager;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;

/**
 * Batch Management Command
 * Batch job yönetim komutu
 */
class BatchManagementCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'queue:batch 
                            {action : Eylem (list, show, cancel, retry, stats, cleanup)}
                            {batch_id? : Batch ID (show, cancel, retry için gerekli)}
                            {--detailed : Detaylı bilgi göster}
                            {--json : JSON formatında çıktı}
                            {--limit=20 : Liste limitı}
                            {--status= : Du<PERSON>a göre filtrele (pending, processing, finished, cancelled, failed)}
                            {--since= : Belirli tarihten sonraki batch\'ler (Y-m-d format)}
                            {--export= : Sonuçları dosyaya export et}';

    /**
     * The console command description.
     */
    protected $description = 'Batch job\'ları yönetir ve izler';

    protected BatchJobManager $batchManager;

    /**
     * Create a new command instance.
     */
    public function __construct(BatchJobManager $batchManager)
    {
        parent::__construct();
        $this->batchManager = $batchManager;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');

        try {
            return match($action) {
                'list' => $this->handleList(),
                'show' => $this->handleShow(),
                'cancel' => $this->handleCancel(),
                'retry' => $this->handleRetry(),
                'stats' => $this->handleStats(),
                'cleanup' => $this->handleCleanup(),
                default => $this->handleInvalidAction($action),
            };
        } catch (\Exception $e) {
            $this->error("❌ Hata: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Batch listesini göster
     */
    protected function handleList(): int
    {
        $this->info("📋 Batch Job Listesi");
        $this->newLine();

        $activeBatches = $this->batchManager->getActiveBatches();
        
        if ($activeBatches->isEmpty()) {
            $this->warn("Aktif batch job bulunamadı.");
            return Command::SUCCESS;
        }

        if ($this->option('json')) {
            $this->line($activeBatches->toJson(JSON_PRETTY_PRINT));
            return Command::SUCCESS;
        }

        $tableData = [];
        foreach ($activeBatches->take($this->option('limit')) as $batch) {
            $tableData[] = [
                substr($batch['id'], 0, 8) . '...',
                $batch['name'] ?? 'N/A',
                $batch['total_jobs'],
                $batch['pending_jobs'],
                $batch['failed_jobs'],
                $batch['progress'] . '%',
                $batch['created_at'],
            ];
        }

        $this->table(
            ['ID', 'Name', 'Total', 'Pending', 'Failed', 'Progress', 'Created'],
            $tableData
        );

        if ($activeBatches->count() > $this->option('limit')) {
            $this->info("... ve " . ($activeBatches->count() - $this->option('limit')) . " batch daha");
        }

        return Command::SUCCESS;
    }

    /**
     * Batch detayını göster
     */
    protected function handleShow(): int
    {
        $batchId = $this->argument('batch_id');
        
        if (!$batchId) {
            $this->error("Batch ID gerekli. Kullanım: queue:batch show <batch_id>");
            return Command::FAILURE;
        }

        $progress = $this->batchManager->getBatchProgress($batchId);
        
        if (isset($progress['error'])) {
            $this->error("❌ " . $progress['error']);
            return Command::FAILURE;
        }

        if ($this->option('json')) {
            $this->line(json_encode($progress, JSON_PRETTY_PRINT));
            return Command::SUCCESS;
        }

        $this->displayBatchDetails($progress);
        return Command::SUCCESS;
    }

    /**
     * Batch'i iptal et
     */
    protected function handleCancel(): int
    {
        $batchId = $this->argument('batch_id');
        
        if (!$batchId) {
            $this->error("Batch ID gerekli. Kullanım: queue:batch cancel <batch_id>");
            return Command::FAILURE;
        }

        if (!$this->confirm("Batch {$batchId} iptal edilsin mi?")) {
            $this->info("İptal işlemi durduruldu.");
            return Command::SUCCESS;
        }

        $success = $this->batchManager->cancelBatch($batchId);
        
        if ($success) {
            $this->info("✅ Batch başarıyla iptal edildi.");
            return Command::SUCCESS;
        } else {
            $this->error("❌ Batch iptal edilemedi.");
            return Command::FAILURE;
        }
    }

    /**
     * Failed batch job'ları retry et
     */
    protected function handleRetry(): int
    {
        $batchId = $this->argument('batch_id');
        
        if (!$batchId) {
            $this->error("Batch ID gerekli. Kullanım: queue:batch retry <batch_id>");
            return Command::FAILURE;
        }

        $progress = $this->batchManager->getBatchProgress($batchId);
        
        if (isset($progress['error'])) {
            $this->error("❌ " . $progress['error']);
            return Command::FAILURE;
        }

        if ($progress['failed_jobs'] == 0) {
            $this->info("Bu batch'de retry edilecek failed job yok.");
            return Command::SUCCESS;
        }

        if (!$this->confirm("Batch {$batchId} için {$progress['failed_jobs']} failed job retry edilsin mi?")) {
            $this->info("Retry işlemi durduruldu.");
            return Command::SUCCESS;
        }

        $success = $this->batchManager->retryFailedBatchJobs($batchId);
        
        if ($success) {
            $this->info("✅ Failed job'lar başarıyla retry edildi.");
            return Command::SUCCESS;
        } else {
            $this->error("❌ Failed job'lar retry edilemedi.");
            return Command::FAILURE;
        }
    }

    /**
     * Batch istatistiklerini göster
     */
    protected function handleStats(): int
    {
        $this->info("📊 Batch Job İstatistikleri");
        $this->newLine();

        $since = $this->option('since') ? \Carbon\Carbon::parse($this->option('since')) : null;
        $stats = $this->batchManager->getBatchStatistics($since);

        if (empty($stats)) {
            $this->warn("İstatistik verisi bulunamadı.");
            return Command::SUCCESS;
        }

        if ($this->option('json')) {
            $this->line(json_encode($stats, JSON_PRETTY_PRINT));
            return Command::SUCCESS;
        }

        $this->displayBatchStatistics($stats);
        return Command::SUCCESS;
    }

    /**
     * Eski batch'leri temizle
     */
    protected function handleCleanup(): int
    {
        $this->info("🧹 Batch Cleanup İşlemi");
        $this->newLine();

        // Bu işlem için özel cleanup logic implement edilmeli
        $this->warn("Cleanup işlemi henüz implement edilmemiş.");
        
        return Command::SUCCESS;
    }

    /**
     * Geçersiz action
     */
    protected function handleInvalidAction(string $action): int
    {
        $this->error("❌ Geçersiz action: {$action}");
        $this->info("Geçerli action'lar: list, show, cancel, retry, stats, cleanup");
        return Command::FAILURE;
    }

    /**
     * Batch detaylarını göster
     */
    protected function displayBatchDetails(array $progress): void
    {
        $this->info("🔍 Batch Detayları");
        $this->newLine();

        $this->table(
            ['Özellik', 'Değer'],
            [
                ['ID', $progress['id']],
                ['Name', $progress['name'] ?? 'N/A'],
                ['Total Jobs', number_format($progress['total_jobs'])],
                ['Processed Jobs', number_format($progress['processed_jobs'])],
                ['Pending Jobs', number_format($progress['pending_jobs'])],
                ['Failed Jobs', number_format($progress['failed_jobs'])],
                ['Progress', $progress['progress_percentage'] . '%'],
                ['Finished', $progress['finished'] ? 'Yes' : 'No'],
                ['Cancelled', $progress['cancelled'] ? 'Yes' : 'No'],
                ['Created At', $progress['created_at']],
                ['Finished At', $progress['finished_at'] ?? 'N/A'],
            ]
        );

        // Custom metrics varsa göster
        if (isset($progress['custom_metrics'])) {
            $this->newLine();
            $this->info("📈 Custom Metrics:");
            foreach ($progress['custom_metrics'] as $key => $value) {
                $this->line("  {$key}: {$value}");
            }
        }

        // Progress bar göster
        if (!$progress['finished'] && !$progress['cancelled']) {
            $this->newLine();
            $progressBar = $this->output->createProgressBar($progress['total_jobs']);
            $progressBar->setProgress($progress['processed_jobs']);
            $progressBar->display();
            $this->newLine(2);
        }
    }

    /**
     * Batch istatistiklerini göster
     */
    protected function displayBatchStatistics(array $stats): void
    {
        $this->table(
            ['Metrik', 'Değer'],
            [
                ['Toplam Batch', number_format($stats['total_batches'])],
                ['Toplam Job', number_format($stats['total_jobs'])],
                ['Toplam Failed Job', number_format($stats['total_failed_jobs'])],
                ['Ortalama Job/Batch', $stats['avg_jobs_per_batch']],
                ['Tamamlanan Batch', number_format($stats['completed_batches'])],
                ['İptal Edilen Batch', number_format($stats['cancelled_batches'])],
                ['Başarı Oranı', $stats['success_rate'] . '%'],
            ]
        );

        $this->newLine();
        $this->info("📅 Periyod: {$stats['period']['since']} - {$stats['period']['until']}");
    }
}
