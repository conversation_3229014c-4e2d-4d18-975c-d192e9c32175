<?php

namespace App\Domain\Payment\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\Payment\Entities\Payment;
use Carbon\Carbon;

/**
 * PaymentInitiated Domain Event
 * Ödeme başlatıldığında tetiklenir
 */
class PaymentInitiated implements DomainEvent
{
    private Payment $payment;
    private Carbon $occurredOn;

    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
        $this->occurredOn = Carbon::now();
    }

    /**
     * Ödemeyi getir
     */
    public function getPayment(): Payment
    {
        return $this->payment;
    }

    /**
     * Event'in gerçekleşme zamanı
     */
    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    /**
     * Event adı
     */
    public function getEventName(): string
    {
        return 'payment.initiated';
    }

    /**
     * Event verisi
     */
    public function getEventData(): array
    {
        return [
            'payment_id' => $this->payment->getId(),
            'transaction_id' => $this->payment->getTransactionId()->getValue(),
            'order_id' => $this->payment->getOrderId(),
            'user_id' => $this->payment->getUserId(),
            'amount' => $this->payment->getAmount()->getAmount()->getAmount(),
            'currency' => $this->payment->getAmount()->getCurrency(),
            'gateway' => $this->payment->getGateway()->getProvider(),
            'payment_method' => $this->payment->getPaymentMethod(),
            'status' => $this->payment->getStatus(),
            'initiated_at' => $this->occurredOn->toISOString(),
        ];
    }

    /**
     * Aggregate ID
     */
    public function getAggregateId(): string
    {
        return (string) $this->payment->getId();
    }

    /**
     * Aggregate Type
     */
    public function getAggregateType(): string
    {
        return 'payment';
    }
}
