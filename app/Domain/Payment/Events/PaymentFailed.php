<?php

namespace App\Domain\Payment\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\Payment\Entities\Payment;
use Carbon\Carbon;

/**
 * PaymentFailed Domain Event
 * Ödeme başarısız olduğunda tetiklenir
 */
class PaymentFailed implements DomainEvent
{
    private Payment $payment;
    private string $failureReason;
    private ?string $errorCode;
    private Carbon $occurredOn;

    public function __construct(Payment $payment, string $failureReason, ?string $errorCode = null)
    {
        $this->payment = $payment;
        $this->failureReason = $failureReason;
        $this->errorCode = $errorCode;
        $this->occurredOn = Carbon::now();
    }

    /**
     * Ödemeyi getir
     */
    public function getPayment(): Payment
    {
        return $this->payment;
    }

    /**
     * Başarısızlık nedenini getir
     */
    public function getFailureReason(): string
    {
        return $this->failureReason;
    }

    /**
     * Hata kodunu getir
     */
    public function getErrorCode(): ?string
    {
        return $this->errorCode;
    }

    /**
     * Event'in gerçekleşme zamanı
     */
    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    /**
     * Event adı
     */
    public function getEventName(): string
    {
        return 'payment.failed';
    }

    /**
     * Event verisi
     */
    public function getEventData(): array
    {
        return [
            'payment_id' => $this->payment->getId(),
            'transaction_id' => $this->payment->getTransactionId()->getValue(),
            'order_id' => $this->payment->getOrderId(),
            'user_id' => $this->payment->getUserId(),
            'amount' => $this->payment->getAmount()->getAmount()->getAmount(),
            'currency' => $this->payment->getAmount()->getCurrency(),
            'gateway' => $this->payment->getGateway()->getProvider(),
            'payment_method' => $this->payment->getPaymentMethod(),
            'status' => $this->payment->getStatus(),
            'failure_reason' => $this->failureReason,
            'error_code' => $this->errorCode,
            'failed_at' => $this->payment->getFailedAt()?->toISOString(),
            'event_time' => $this->occurredOn->toISOString(),
        ];
    }

    /**
     * Aggregate ID
     */
    public function getAggregateId(): string
    {
        return (string) $this->payment->getId();
    }

    /**
     * Aggregate Type
     */
    public function getAggregateType(): string
    {
        return 'payment';
    }
}
