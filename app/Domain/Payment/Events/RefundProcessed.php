<?php

namespace App\Domain\Payment\Events;

use App\Domain\Shared\Events\DomainEvent;
use App\Domain\Payment\Entities\Refund;
use Carbon\Carbon;

/**
 * RefundProcessed Domain Event
 * İade işlendiğinde tetiklenir
 */
class RefundProcessed implements DomainEvent
{
    private Refund $refund;
    private Carbon $occurredOn;

    public function __construct(Refund $refund)
    {
        $this->refund = $refund;
        $this->occurredOn = Carbon::now();
    }

    /**
     * İadeyi getir
     */
    public function getRefund(): Refund
    {
        return $this->refund;
    }

    /**
     * Event'in gerçekleşme zamanı
     */
    public function occurredOn(): Carbon
    {
        return $this->occurredOn;
    }

    /**
     * Event adı
     */
    public function getEventName(): string
    {
        return 'refund.processed';
    }

    /**
     * Event verisi
     */
    public function getEventData(): array
    {
        return [
            'refund_id' => $this->refund->getId(),
            'payment_id' => $this->refund->getPaymentId(),
            'transaction_id' => $this->refund->getTransactionId()->getValue(),
            'amount' => $this->refund->getAmount()->getAmount()->getAmount(),
            'currency' => $this->refund->getAmount()->getCurrency(),
            'reason' => $this->refund->getReason(),
            'status' => $this->refund->getStatus(),
            'processed_at' => $this->refund->getProcessedAt()?->toISOString(),
            'event_time' => $this->occurredOn->toISOString(),
        ];
    }

    /**
     * Aggregate ID
     */
    public function getAggregateId(): string
    {
        return (string) $this->refund->getId();
    }

    /**
     * Aggregate Type
     */
    public function getAggregateType(): string
    {
        return 'refund';
    }
}
