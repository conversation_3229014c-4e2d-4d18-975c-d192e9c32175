<?php

namespace App\Providers;

use App\Models\Category;
use App\Models\Product;
use App\Observers\CategoryObserver;
use App\Observers\ProductObserver;
use Illuminate\Support\ServiceProvider;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Queue Infrastructure Services (Phase 5C)
        $this->app->singleton(\App\Core\Infrastructure\Queue\Services\JobPriorityManager::class);
        $this->app->singleton(\App\Core\Infrastructure\Queue\Services\BatchJobManager::class);
        $this->app->singleton(\App\Core\Infrastructure\Queue\Services\QueueHealthMonitor::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Inertia.js hata ayıklama
        Inertia::share('app.debug', config('app.debug'));

        // <PERSON><PERSON><PERSON> sayfaları için özel loglama ekle
        Route::matched(function (\Illuminate\Routing\Events\RouteMatched $event) {
            if ($event->route->getName() === 'categories.show') {
                Log::info('Categories.show route matched', [
                    'route' => $event->route->getName(),
                    'parameters' => $event->route->parameters(),
                    'uri' => $event->request->getRequestUri(),
                ]);
            }
        });

        // Observer'ları kaydet
        Product::observe(ProductObserver::class);
        Category::observe(CategoryObserver::class);
    }
}
