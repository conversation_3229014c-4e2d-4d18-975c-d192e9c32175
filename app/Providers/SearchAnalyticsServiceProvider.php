<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Core\Infrastructure\Search\ElasticsearchService;
use App\Core\Infrastructure\Search\SearchIndexManager;
use App\Core\Infrastructure\Analytics\AnalyticsEngine;
use App\Core\Infrastructure\Analytics\RealTimeProcessor;

/**
 * SearchAnalyticsServiceProvider
 * Search & Analytics infrastructure service provider
 */
class SearchAnalyticsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Elasticsearch Service
        $this->app->singleton(ElasticsearchService::class, function ($app) {
            return new ElasticsearchService();
        });

        // Search Index Manager
        $this->app->singleton(SearchIndexManager::class, function ($app) {
            return new SearchIndexManager(
                $app->make(ElasticsearchService::class)
            );
        });

        // Real Time Processor
        $this->app->singleton(RealTimeProcessor::class, function ($app) {
            return new RealTimeProcessor();
        });

        // Analytics Engine
        $this->app->singleton(AnalyticsEngine::class, function ($app) {
            return new AnalyticsEngine(
                $app->make(ElasticsearchService::class),
                $app->make(SearchIndexManager::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Elasticsearch configuration yayınla
        $this->publishes([
            __DIR__.'/../../config/elasticsearch.php' => config_path('elasticsearch.php'),
        ], 'elasticsearch-config');

        // Console commands kaydet
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\Search\ElasticsearchIndexCommand::class,
            ]);
        }

        // Event listeners kaydet
        $this->registerEventListeners();

        // Middleware kaydet
        $this->registerMiddleware();
    }

    /**
     * Event listeners kaydet
     */
    protected function registerEventListeners(): void
    {
        // Product events
        \App\Models\Product::created(function ($product) {
            $indexManager = app(SearchIndexManager::class);
            $indexManager->indexProduct($product);
        });

        \App\Models\Product::updated(function ($product) {
            $indexManager = app(SearchIndexManager::class);
            $indexManager->indexProduct($product);
        });

        \App\Models\Product::deleted(function ($product) {
            $indexManager = app(SearchIndexManager::class);
            $indexManager->removeProduct($product->id);
        });

        // Analytics events
        $this->app['events']->listen('product.viewed', function ($event) {
            $analyticsEngine = app(AnalyticsEngine::class);
            $realTimeProcessor = app(RealTimeProcessor::class);
            
            $eventData = [
                'product_id' => $event->product->id,
                'category_id' => $event->product->category_id,
                'user_id' => auth()->id(),
            ];

            $analyticsEngine->recordEvent('product_view', $eventData);
            $realTimeProcessor->processEvent(array_merge($eventData, [
                'event_type' => 'product_view',
                'timestamp' => now()->toISOString(),
            ]));
        });

        $this->app['events']->listen('cart.item_added', function ($event) {
            $analyticsEngine = app(AnalyticsEngine::class);
            $realTimeProcessor = app(RealTimeProcessor::class);
            
            $eventData = [
                'product_id' => $event->product->id,
                'quantity' => $event->quantity,
                'user_id' => auth()->id(),
            ];

            $analyticsEngine->recordEvent('add_to_cart', $eventData);
            $realTimeProcessor->processEvent(array_merge($eventData, [
                'event_type' => 'add_to_cart',
                'timestamp' => now()->toISOString(),
            ]));
        });

        $this->app['events']->listen('order.completed', function ($event) {
            $analyticsEngine = app(AnalyticsEngine::class);
            $realTimeProcessor = app(RealTimeProcessor::class);
            
            $products = [];
            foreach ($event->order->items as $item) {
                $products[] = [
                    'id' => $item->product_id,
                    'quantity' => $item->quantity,
                    'price' => $item->price,
                ];
            }

            $eventData = [
                'order_id' => $event->order->id,
                'user_id' => $event->order->user_id,
                'revenue' => $event->order->total,
                'products' => $products,
            ];

            $analyticsEngine->recordEvent('purchase', $eventData);
            $realTimeProcessor->processEvent(array_merge($eventData, [
                'event_type' => 'purchase',
                'timestamp' => now()->toISOString(),
            ]));
        });
    }

    /**
     * Middleware kaydet
     */
    protected function registerMiddleware(): void
    {
        // Page view tracking middleware
        $this->app['router']->pushMiddlewareToGroup('web', \App\Http\Middleware\TrackPageViews::class);
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            ElasticsearchService::class,
            SearchIndexManager::class,
            AnalyticsEngine::class,
            RealTimeProcessor::class,
        ];
    }
}
