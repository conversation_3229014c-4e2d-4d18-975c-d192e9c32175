<?php

namespace App\Infrastructure\Products\Services;

use App\Domain\Products\Repositories\ProductRepositoryInterface;
use App\Domain\Products\Entities\Product;

/**
 * Product Search Service
 * Ürün arama işlemleri için infrastructure service
 */
class ProductSearchService
{
    private ProductRepositoryInterface $repository;
    private string $searchEngine;
    private string $searchIndex;

    public function __construct(
        ProductRepositoryInterface $repository,
        string $searchEngine = 'database',
        string $searchIndex = 'products'
    ) {
        $this->repository = $repository;
        $this->searchEngine = $searchEngine;
        $this->searchIndex = $searchIndex;
    }

    /**
     * Gelişmiş ürün arama
     */
    public function search(array $params): array
    {
        $criteria = $this->buildSearchCriteria($params);
        $limit = $params['limit'] ?? 20;
        $offset = $params['offset'] ?? 0;

        switch ($this->searchEngine) {
            case 'elasticsearch':
                return $this->searchWithElasticsearch($criteria, $limit, $offset);
            case 'algolia':
                return $this->searchWithAlgolia($criteria, $limit, $offset);
            default:
                return $this->searchWithDatabase($criteria, $limit, $offset);
        }
    }

    /**
     * Otomatik tamamlama önerileri
     */
    public function getSuggestions(string $query, int $limit = 10): array
    {
        $suggestions = [];

        // Ürün adlarından öneriler
        $productSuggestions = $this->repository->search([
            'name' => $query
        ], $limit, 0);

        foreach ($productSuggestions as $product) {
            $suggestions[] = [
                'type' => 'product',
                'text' => $product->getName(),
                'url' => "/products/{$product->getSlug()}",
                'image' => $this->getProductThumbnail($product),
            ];
        }

        // Kategori önerilerini de ekleyebiliriz
        // $categorySuggestions = $this->getCategorySuggestions($query, $limit);

        return array_slice($suggestions, 0, $limit);
    }

    /**
     * Popüler arama terimleri
     */
    public function getPopularSearchTerms(int $limit = 10): array
    {
        // Bu bilgiyi cache'den veya analytics'ten alabiliriz
        // Şimdilik mock data
        return [
            'laptop',
            'telefon',
            'kulaklık',
            'klavye',
            'mouse',
            'monitor',
            'tablet',
            'kamera',
            'oyun',
            'kitap'
        ];
    }

    /**
     * Arama filtrelerini al
     */
    public function getAvailableFilters(array $searchCriteria = []): array
    {
        // Mevcut arama kriterlerine göre kullanılabilir filtreleri döndür
        return [
            'categories' => $this->getAvailableCategories($searchCriteria),
            'price_ranges' => $this->getAvailablePriceRanges($searchCriteria),
            'brands' => $this->getAvailableBrands($searchCriteria),
            'attributes' => $this->getAvailableAttributes($searchCriteria),
        ];
    }

    /**
     * Arama kriterlerini oluştur
     */
    private function buildSearchCriteria(array $params): array
    {
        $criteria = [];

        // Metin arama
        if (!empty($params['q'])) {
            $criteria['name'] = $params['q'];
        }

        // Kategori filtresi
        if (!empty($params['category_id'])) {
            $criteria['category_id'] = $params['category_id'];
        }

        // Fiyat aralığı
        if (!empty($params['min_price'])) {
            $criteria['min_price'] = (float) $params['min_price'];
        }
        if (!empty($params['max_price'])) {
            $criteria['max_price'] = (float) $params['max_price'];
        }

        // Stok durumu
        if (!empty($params['in_stock'])) {
            $criteria['in_stock'] = filter_var($params['in_stock'], FILTER_VALIDATE_BOOLEAN);
        }

        // İndirimli ürünler
        if (!empty($params['on_sale'])) {
            $criteria['on_sale'] = filter_var($params['on_sale'], FILTER_VALIDATE_BOOLEAN);
        }

        // Öne çıkan ürünler
        if (!empty($params['featured'])) {
            $criteria['featured'] = filter_var($params['featured'], FILTER_VALIDATE_BOOLEAN);
        }

        // Marka filtresi
        if (!empty($params['brand'])) {
            $criteria['brand'] = $params['brand'];
        }

        // Özel özellikler
        if (!empty($params['attributes'])) {
            $criteria['attributes'] = $params['attributes'];
        }

        return $criteria;
    }

    /**
     * Database ile arama
     */
    private function searchWithDatabase(array $criteria, int $limit, int $offset): array
    {
        $products = $this->repository->search($criteria, $limit, $offset);
        $total = $this->repository->count($criteria);

        return [
            'products' => $products,
            'total' => $total,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $total,
        ];
    }

    /**
     * Elasticsearch ile arama
     */
    private function searchWithElasticsearch(array $criteria, int $limit, int $offset): array
    {
        // Elasticsearch geçici olarak devre dışı - database search'e fallback
        \Log::info('Elasticsearch temporarily disabled, falling back to database search');
        return $this->searchWithDatabase($criteria, $limit, $offset);
    }

    /**
     * Algolia ile arama
     */
    private function searchWithAlgolia(array $criteria, int $limit, int $offset): array
    {
        // Algolia implementasyonu
        // Bu kısım Algolia client'ı gerektirir
        throw new \Exception('Algolia integration not implemented yet');
    }

    /**
     * Kullanılabilir kategorileri al
     */
    private function getAvailableCategories(array $searchCriteria): array
    {
        // Bu bilgiyi category repository'den alabiliriz
        return [
            ['id' => 1, 'name' => 'Elektronik', 'count' => 150],
            ['id' => 2, 'name' => 'Giyim', 'count' => 200],
            ['id' => 3, 'name' => 'Ev & Yaşam', 'count' => 100],
        ];
    }

    /**
     * Kullanılabilir fiyat aralıklarını al
     */
    private function getAvailablePriceRanges(array $searchCriteria): array
    {
        return [
            ['min' => 0, 'max' => 100, 'label' => '0-100 TL', 'count' => 50],
            ['min' => 100, 'max' => 500, 'label' => '100-500 TL', 'count' => 120],
            ['min' => 500, 'max' => 1000, 'label' => '500-1000 TL', 'count' => 80],
            ['min' => 1000, 'max' => null, 'label' => '1000+ TL', 'count' => 30],
        ];
    }

    /**
     * Kullanılabilir markaları al
     */
    private function getAvailableBrands(array $searchCriteria): array
    {
        return [
            ['name' => 'Apple', 'count' => 25],
            ['name' => 'Samsung', 'count' => 30],
            ['name' => 'Sony', 'count' => 15],
        ];
    }

    /**
     * Kullanılabilir özellikleri al
     */
    private function getAvailableAttributes(array $searchCriteria): array
    {
        return [
            'color' => [
                ['value' => 'red', 'label' => 'Kırmızı', 'count' => 10],
                ['value' => 'blue', 'label' => 'Mavi', 'count' => 15],
                ['value' => 'black', 'label' => 'Siyah', 'count' => 20],
            ],
            'size' => [
                ['value' => 'S', 'label' => 'Small', 'count' => 8],
                ['value' => 'M', 'label' => 'Medium', 'count' => 12],
                ['value' => 'L', 'label' => 'Large', 'count' => 10],
            ],
        ];
    }

    /**
     * Ürün thumbnail'ını al
     */
    private function getProductThumbnail(Product $product): ?string
    {
        // Product entity'sinden thumbnail URL'ini al
        // Bu kısım Product entity'sinde image management ile ilgili method gerektirir
        return null; // Şimdilik null
    }

    /**
     * Arama motorunu değiştir
     */
    public function setSearchEngine(string $engine): void
    {
        $allowedEngines = ['database', 'elasticsearch', 'algolia'];
        
        if (!in_array($engine, $allowedEngines)) {
            throw new \InvalidArgumentException("Unsupported search engine: {$engine}");
        }

        $this->searchEngine = $engine;
    }

    /**
     * Mevcut arama motorunu al
     */
    public function getSearchEngine(): string
    {
        return $this->searchEngine;
    }
}
