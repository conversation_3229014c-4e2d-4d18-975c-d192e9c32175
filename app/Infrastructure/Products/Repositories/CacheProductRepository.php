<?php

namespace App\Infrastructure\Products\Repositories;

use App\Domain\Products\Entities\Product;
use App\Domain\Products\Repositories\ProductRepositoryInterface;
use App\Domain\Products\ValueObjects\SKU;
use Illuminate\Support\Facades\Cache;

/**
 * Cache Product Repository Decorator
 * Product repository'sine cache katmanı ekler
 */
class CacheProductRepository implements ProductRepositoryInterface
{
    private ProductRepositoryInterface $repository;
    private int $cacheTtl;
    private string $cachePrefix;

    public function __construct(
        ProductRepositoryInterface $repository,
        int $cacheTtl = 3600,
        string $cachePrefix = 'products'
    ) {
        $this->repository = $repository;
        $this->cacheTtl = $cacheTtl;
        $this->cachePrefix = $cachePrefix;
    }

    public function save(Product $product): Product
    {
        $result = $this->repository->save($product);
        
        // Cache'i temizle
        $this->clearProductCache($product->getId());
        $this->clearListCaches();
        
        return $result;
    }

    public function findById(int $id): ?Product
    {
        $cacheKey = $this->getCacheKey("product.{$id}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($id) {
            return $this->repository->findById($id);
        });
    }

    public function findBySlug(string $slug): ?Product
    {
        $cacheKey = $this->getCacheKey("product.slug.{$slug}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($slug) {
            return $this->repository->findBySlug($slug);
        });
    }

    public function findBySKU(SKU $sku): ?Product
    {
        $cacheKey = $this->getCacheKey("product.sku.{$sku->getValue()}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($sku) {
            return $this->repository->findBySKU($sku);
        });
    }

    public function findByCategoryId(int $categoryId, int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("products.category.{$categoryId}.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($categoryId, $limit, $offset) {
            return $this->repository->findByCategoryId($categoryId, $limit, $offset);
        });
    }

    public function findActive(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("products.active.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findActive($limit, $offset);
        });
    }

    public function findFeatured(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("products.featured.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findFeatured($limit, $offset);
        });
    }

    public function findOnSale(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("products.on_sale.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findOnSale($limit, $offset);
        });
    }

    public function findInStock(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("products.in_stock.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findInStock($limit, $offset);
        });
    }

    public function findOutOfStock(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("products.out_of_stock.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findOutOfStock($limit, $offset);
        });
    }

    public function findLowStock(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("products.low_stock.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findLowStock($limit, $offset);
        });
    }

    public function search(array $criteria, int $limit = 10, int $offset = 0): array
    {
        // Search sonuçları cache'lenmez (çok dinamik)
        return $this->repository->search($criteria, $limit, $offset);
    }

    public function count(array $criteria = []): int
    {
        if (empty($criteria)) {
            $cacheKey = $this->getCacheKey("products.count.all");
            
            return Cache::remember($cacheKey, $this->cacheTtl, function () use ($criteria) {
                return $this->repository->count($criteria);
            });
        }
        
        return $this->repository->count($criteria);
    }

    public function countByCategoryId(int $categoryId): int
    {
        $cacheKey = $this->getCacheKey("products.count.category.{$categoryId}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($categoryId) {
            return $this->repository->countByCategoryId($categoryId);
        });
    }

    public function delete(Product $product): bool
    {
        $result = $this->repository->delete($product);
        
        if ($result) {
            $this->clearProductCache($product->getId());
            $this->clearListCaches();
        }
        
        return $result;
    }

    public function deleteById(int $id): bool
    {
        $result = $this->repository->deleteById($id);
        
        if ($result) {
            $this->clearProductCache($id);
            $this->clearListCaches();
        }
        
        return $result;
    }

    public function exists(int $id): bool
    {
        $cacheKey = $this->getCacheKey("product.exists.{$id}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($id) {
            return $this->repository->exists($id);
        });
    }

    public function existsBySKU(SKU $sku): bool
    {
        $cacheKey = $this->getCacheKey("product.exists.sku.{$sku->getValue()}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($sku) {
            return $this->repository->existsBySKU($sku);
        });
    }

    public function existsBySlug(string $slug): bool
    {
        $cacheKey = $this->getCacheKey("product.exists.slug.{$slug}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($slug) {
            return $this->repository->existsBySlug($slug);
        });
    }

    public function findMostViewed(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("products.most_viewed.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findMostViewed($limit, $offset);
        });
    }

    public function findLatest(int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("products.latest.{$limit}.{$offset}");
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($limit, $offset) {
            return $this->repository->findLatest($limit, $offset);
        });
    }

    public function findByPriceRange(float $minPrice, float $maxPrice, int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("products.price_range.{$minPrice}.{$maxPrice}.{$limit}.{$offset}");

        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($minPrice, $maxPrice, $limit, $offset) {
            return $this->repository->findByPriceRange($minPrice, $maxPrice, $limit, $offset);
        });
    }

    public function findSimilar(Product $product, int $limit = 10): array
    {
        $cacheKey = $this->getCacheKey("products.similar.{$product->getId()}.{$limit}");

        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($product, $limit) {
            return $this->repository->findSimilar($product, $limit);
        });
    }

    public function findRelated(Product $product, int $limit = 10): array
    {
        $cacheKey = $this->getCacheKey("products.related.{$product->getId()}.{$limit}");

        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($product, $limit) {
            return $this->repository->findRelated($product, $limit);
        });
    }

    public function findRandom(int $limit = 10): array
    {
        // Random sonuçlar cache'lenmez
        return $this->repository->findRandom($limit);
    }

    public function findUpdatedAfter(\DateTime $date, int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("products.updated_after.{$date->format('Y-m-d')}.{$limit}.{$offset}");

        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($date, $limit, $offset) {
            return $this->repository->findUpdatedAfter($date, $limit, $offset);
        });
    }

    public function findCreatedAfter(\DateTime $date, int $limit = 10, int $offset = 0): array
    {
        $cacheKey = $this->getCacheKey("products.created_after.{$date->format('Y-m-d')}.{$limit}.{$offset}");

        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($date, $limit, $offset) {
            return $this->repository->findCreatedAfter($date, $limit, $offset);
        });
    }

    public function incrementViewCount(int $productId): void
    {
        $this->repository->incrementViewCount($productId);

        // View count cache'ini temizle
        $this->clearProductCache($productId);
        Cache::forget($this->getCacheKey("products.most_viewed.*"));
    }

    public function bulkUpdateStock(array $stockUpdates): bool
    {
        $result = $this->repository->bulkUpdateStock($stockUpdates);

        if ($result) {
            // Etkilenen ürünlerin cache'ini temizle
            foreach ($stockUpdates as $update) {
                if (isset($update['product_id'])) {
                    $this->clearProductCache($update['product_id']);
                }
            }
            $this->clearListCaches();
        }

        return $result;
    }

    public function bulkUpdatePrices(array $priceUpdates): bool
    {
        $result = $this->repository->bulkUpdatePrices($priceUpdates);

        if ($result) {
            // Etkilenen ürünlerin cache'ini temizle
            foreach ($priceUpdates as $update) {
                if (isset($update['product_id'])) {
                    $this->clearProductCache($update['product_id']);
                }
            }
            $this->clearListCaches();
        }

        return $result;
    }

    public function bulkUpdateStatus(array $productIds, bool $status): bool
    {
        $result = $this->repository->bulkUpdateStatus($productIds, $status);

        if ($result) {
            foreach ($productIds as $productId) {
                $this->clearProductCache($productId);
            }
            $this->clearListCaches();
        }

        return $result;
    }

    public function bulkUpdateCategory(array $productIds, int $categoryId): bool
    {
        $result = $this->repository->bulkUpdateCategory($productIds, $categoryId);

        if ($result) {
            foreach ($productIds as $productId) {
                $this->clearProductCache($productId);
            }
            $this->clearListCaches();
        }

        return $result;
    }

    public function getStatistics(): array
    {
        $cacheKey = $this->getCacheKey("products.statistics");

        return Cache::remember($cacheKey, $this->cacheTtl, function () {
            return $this->repository->getStatistics();
        });
    }

    public function getCategoryStatistics(int $categoryId): array
    {
        $cacheKey = $this->getCacheKey("products.category_statistics.{$categoryId}");

        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($categoryId) {
            return $this->repository->getCategoryStatistics($categoryId);
        });
    }

    public function getStockStatistics(): array
    {
        $cacheKey = $this->getCacheKey("products.stock_statistics");

        return Cache::remember($cacheKey, $this->cacheTtl, function () {
            return $this->repository->getStockStatistics();
        });
    }

    public function getPriceStatistics(): array
    {
        $cacheKey = $this->getCacheKey("products.price_statistics");

        return Cache::remember($cacheKey, $this->cacheTtl, function () {
            return $this->repository->getPriceStatistics();
        });
    }

    /**
     * Cache key oluştur
     */
    private function getCacheKey(string $key): string
    {
        return "{$this->cachePrefix}.{$key}";
    }

    /**
     * Ürün cache'ini temizle
     */
    private function clearProductCache(?int $productId): void
    {
        if (!$productId) {
            return;
        }

        $patterns = [
            "product.{$productId}",
            "product.exists.{$productId}",
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($this->getCacheKey($pattern));
        }
    }

    /**
     * Liste cache'lerini temizle
     */
    private function clearListCaches(): void
    {
        $patterns = [
            'products.active.*',
            'products.featured.*',
            'products.on_sale.*',
            'products.in_stock.*',
            'products.out_of_stock.*',
            'products.low_stock.*',
            'products.most_viewed.*',
            'products.latest.*',
            'products.count.*',
            'products.category.*',
            'products.price_range.*',
        ];

        // Cache tag'leri kullanılabilirse daha etkili olur
        // Şimdilik basit pattern matching
        foreach ($patterns as $pattern) {
            Cache::flush(); // Geliştirme aşamasında - production'da daha spesifik olmalı
        }
    }
}
