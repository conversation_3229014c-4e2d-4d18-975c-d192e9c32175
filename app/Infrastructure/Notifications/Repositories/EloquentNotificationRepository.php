<?php

namespace App\Infrastructure\Notifications\Repositories;

use App\Domain\Notifications\Entities\Notification;
use App\Domain\Notifications\Repositories\NotificationRepositoryInterface;
use App\Domain\Notifications\ValueObjects\NotificationType;
use App\Domain\Notifications\ValueObjects\NotificationChannel;
use App\Domain\Notifications\ValueObjects\NotificationStatus;
use App\Domain\Notifications\ValueObjects\NotificationPriority;
use App\Domain\Notifications\ValueObjects\RecipientInfo;
use App\Infrastructure\Notifications\Models\EloquentNotification;
use App\Infrastructure\Notifications\Mappers\NotificationMapper;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * EloquentNotificationRepository
 * Notification domain repository'sinin Eloquent implementasyonu
 */
class EloquentNotificationRepository implements NotificationRepositoryInterface
{
    public function __construct(
        private NotificationMapper $mapper
    ) {}

    /**
     * <PERSON><PERSON><PERSON>im kaydet
     */
    public function save(Notification $notification): Notification
    {
        try {
            DB::beginTransaction();

            $eloquentModel = $this->mapper->toEloquent($notification);
            $eloquentModel->save();

            // ID'yi domain entity'ye set et
            if (!$notification->getId()) {
                $this->mapper->setId($notification, $eloquentModel->id);
            }

            DB::commit();

            // Cache'i temizle
            $this->clearNotificationCache($notification);

            Log::info('Notification saved successfully', [
                'notification_id' => $notification->getId(),
                'type' => $notification->getType()->getValue(),
                'channel' => $notification->getChannel()->getValue(),
            ]);

            return $notification;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to save notification', [
                'error' => $e->getMessage(),
                'type' => $notification->getType()->getValue(),
                'channel' => $notification->getChannel()->getValue(),
            ]);

            throw new \RuntimeException('Failed to save notification: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * ID ile bildirim bul
     */
    public function findById(int $id): ?Notification
    {
        $cacheKey = "notification:{$id}";

        return Cache::remember($cacheKey, 300, function () use ($id) {
            $eloquentModel = EloquentNotification::find($id);

            if (!$eloquentModel) {
                return null;
            }

            return $this->mapper->toDomain($eloquentModel);
        });
    }

    /**
     * Bildirimi sil
     */
    public function delete(Notification $notification): bool
    {
        if (!$notification->getId()) {
            return false;
        }

        return $this->deleteById($notification->getId());
    }

    /**
     * ID ile bildirimi sil
     */
    public function deleteById(int $id): bool
    {
        try {
            $result = EloquentNotification::where('id', $id)->delete();

            if ($result) {
                Cache::forget("notification:{$id}");
                Log::info('Notification deleted successfully', ['notification_id' => $id]);
            }

            return $result > 0;

        } catch (\Exception $e) {
            Log::error('Failed to delete notification', [
                'notification_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Bildirim var mı kontrol et
     */
    public function exists(int $id): bool
    {
        return EloquentNotification::where('id', $id)->exists();
    }

    /**
     * Alıcıya göre bildirimleri al
     */
    public function findByRecipient(RecipientInfo $recipient, int $limit = 10, int $offset = 0): array
    {
        $query = EloquentNotification::where('recipient_type', $recipient->getType());

        if ($recipient->getId()) {
            $query->where('recipient_user_id', $recipient->getId());
        }

        if ($recipient->getEmail()) {
            $query->where('recipient_email', $recipient->getEmail());
        }

        if ($recipient->getPhone()) {
            $query->where('recipient_phone', $recipient->getPhone());
        }

        $eloquentModels = $query->orderBy('created_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Duruma göre bildirimleri al
     */
    public function findByStatus(NotificationStatus $status, int $limit = 10, int $offset = 0): array
    {
        $eloquentModels = EloquentNotification::where('status', $status->getValue())
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Kanala göre bildirimleri al
     */
    public function findByChannel(NotificationChannel $channel, int $limit = 10, int $offset = 0): array
    {
        $eloquentModels = EloquentNotification::where('channel', $channel->getValue())
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Tipe göre bildirimleri al
     */
    public function findByType(NotificationType $type, int $limit = 10, int $offset = 0): array
    {
        $eloquentModels = EloquentNotification::where('type', $type->getValue())
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Önceliğe göre bildirimleri al
     */
    public function findByPriority(NotificationPriority $priority, int $limit = 10, int $offset = 0): array
    {
        $eloquentModels = EloquentNotification::where('priority', $priority->getValue())
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Bekleyen bildirimleri al
     */
    public function findPending(int $limit = 10, int $offset = 0): array
    {
        $eloquentModels = EloquentNotification::where('status', 'pending')
            ->orderBy('priority', 'desc')
            ->orderBy('created_at', 'asc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Gönderilmeyi bekleyen bildirimleri al (zamanlanmış)
     */
    public function findScheduledForDelivery(Carbon $before = null, int $limit = 10, int $offset = 0): array
    {
        $before = $before ?: Carbon::now();

        $eloquentModels = EloquentNotification::where('status', 'pending')
            ->where(function ($query) use ($before) {
                $query->whereNull('scheduled_at')
                    ->orWhere('scheduled_at', '<=', $before);
            })
            ->whereNull('expires_at')
            ->orWhere('expires_at', '>', Carbon::now())
            ->orderBy('priority', 'desc')
            ->orderBy('scheduled_at', 'asc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Yeniden deneme gereken bildirimleri al
     */
    public function findForRetry(int $limit = 10, int $offset = 0): array
    {
        $eloquentModels = EloquentNotification::where('status', 'retrying')
            ->whereRaw('retry_count < max_retries')
            ->orderBy('priority', 'desc')
            ->orderBy('updated_at', 'asc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Başarısız bildirimleri al
     */
    public function findFailed(int $limit = 10, int $offset = 0): array
    {
        $eloquentModels = EloquentNotification::where('status', 'failed')
            ->orderBy('updated_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Süresi dolmuş bildirimleri al
     */
    public function findExpired(int $limit = 10, int $offset = 0): array
    {
        $eloquentModels = EloquentNotification::where('expires_at', '<', Carbon::now())
            ->whereIn('status', ['pending', 'retrying'])
            ->orderBy('expires_at', 'asc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Yüksek öncelikli bildirimleri al
     */
    public function findHighPriority(int $limit = 10, int $offset = 0): array
    {
        $eloquentModels = EloquentNotification::whereIn('priority', ['critical', 'urgent', 'high'])
            ->orderBy('priority', 'desc')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Okunmamış bildirimleri al
     */
    public function findUnread(RecipientInfo $recipient, int $limit = 10, int $offset = 0): array
    {
        $query = EloquentNotification::where('recipient_type', $recipient->getType())
            ->where('status', 'sent')
            ->whereNull('read_at');

        if ($recipient->getId()) {
            $query->where('recipient_user_id', $recipient->getId());
        }

        $eloquentModels = $query->orderBy('created_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Eloquent model'leri domain entity'lere çevir
     */
    private function mapToDomainArray($eloquentModels): array
    {
        return $eloquentModels->map(function ($model) {
            return $this->mapper->toDomain($model);
        })->toArray();
    }

    /**
     * Tarih aralığında bildirimleri al
     */
    public function findByDateRange(Carbon $from, Carbon $to, int $limit = 10, int $offset = 0): array
    {
        $eloquentModels = EloquentNotification::whereBetween('created_at', [$from, $to])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Template'e göre bildirimleri al
     */
    public function findByTemplate(string $templateId, int $limit = 10, int $offset = 0): array
    {
        $eloquentModels = EloquentNotification::where('template_id', $templateId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Toplu bildirim kaydet
     */
    public function saveBatch(array $notifications): array
    {
        try {
            DB::beginTransaction();

            $savedNotifications = [];

            foreach ($notifications as $notification) {
                $savedNotifications[] = $this->save($notification);
            }

            DB::commit();

            Log::info('Batch notifications saved successfully', [
                'count' => count($savedNotifications)
            ]);

            return $savedNotifications;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to save batch notifications', [
                'error' => $e->getMessage(),
                'count' => count($notifications)
            ]);

            throw new \RuntimeException('Failed to save batch notifications: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Toplu durum güncelleme
     */
    public function updateStatusBatch(array $notificationIds, NotificationStatus $status): int
    {
        try {
            $updated = EloquentNotification::whereIn('id', $notificationIds)
                ->update([
                    'status' => $status->getValue(),
                    'updated_at' => Carbon::now()
                ]);

            // Cache'leri temizle
            foreach ($notificationIds as $id) {
                Cache::forget("notification:{$id}");
            }

            Log::info('Batch status update completed', [
                'notification_ids' => $notificationIds,
                'new_status' => $status->getValue(),
                'updated_count' => $updated
            ]);

            return $updated;

        } catch (\Exception $e) {
            Log::error('Failed to update batch status', [
                'notification_ids' => $notificationIds,
                'status' => $status->getValue(),
                'error' => $e->getMessage()
            ]);

            throw new \RuntimeException('Failed to update batch status: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Eski bildirimleri temizle
     */
    public function deleteOlderThan(Carbon $date): int
    {
        try {
            $deleted = EloquentNotification::where('created_at', '<', $date)
                ->whereIn('status', ['sent', 'delivered', 'read', 'failed', 'cancelled', 'expired'])
                ->delete();

            Log::info('Old notifications cleaned up', [
                'before_date' => $date->toDateString(),
                'deleted_count' => $deleted
            ]);

            return $deleted;

        } catch (\Exception $e) {
            Log::error('Failed to clean up old notifications', [
                'before_date' => $date->toDateString(),
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }

    /**
     * İstatistikleri al
     */
    public function getStatistics(Carbon $from = null, Carbon $to = null): array
    {
        $from = $from ?: Carbon::now()->subDays(30);
        $to = $to ?: Carbon::now();

        try {
            $stats = EloquentNotification::whereBetween('created_at', [$from, $to])
                ->selectRaw('
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = "sent" THEN 1 END) as sent,
                    COUNT(CASE WHEN status = "delivered" THEN 1 END) as delivered,
                    COUNT(CASE WHEN status = "read" THEN 1 END) as read,
                    COUNT(CASE WHEN status = "failed" THEN 1 END) as failed,
                    COUNT(CASE WHEN status = "cancelled" THEN 1 END) as cancelled,
                    AVG(CASE WHEN sent_at IS NOT NULL THEN TIMESTAMPDIFF(SECOND, created_at, sent_at) END) as avg_delivery_time,
                    AVG(CASE WHEN read_at IS NOT NULL AND sent_at IS NOT NULL THEN TIMESTAMPDIFF(SECOND, sent_at, read_at) END) as avg_read_time
                ')
                ->first();

            return [
                'total' => $stats->total ?? 0,
                'sent' => $stats->sent ?? 0,
                'delivered' => $stats->delivered ?? 0,
                'read' => $stats->read ?? 0,
                'failed' => $stats->failed ?? 0,
                'cancelled' => $stats->cancelled ?? 0,
                'delivery_rate' => $stats->total > 0 ? round(($stats->sent / $stats->total) * 100, 2) : 0,
                'read_rate' => $stats->sent > 0 ? round(($stats->read / $stats->sent) * 100, 2) : 0,
                'failure_rate' => $stats->total > 0 ? round(($stats->failed / $stats->total) * 100, 2) : 0,
                'avg_delivery_time' => $stats->avg_delivery_time ? round($stats->avg_delivery_time, 2) : null,
                'avg_read_time' => $stats->avg_read_time ? round($stats->avg_read_time, 2) : null,
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get notification statistics', [
                'from' => $from->toDateString(),
                'to' => $to->toDateString(),
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Kanal istatistikleri al
     */
    public function getChannelStatistics(Carbon $from = null, Carbon $to = null): array
    {
        $from = $from ?: Carbon::now()->subDays(30);
        $to = $to ?: Carbon::now();

        try {
            $stats = EloquentNotification::whereBetween('created_at', [$from, $to])
                ->selectRaw('
                    channel,
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = "sent" THEN 1 END) as sent,
                    COUNT(CASE WHEN status = "failed" THEN 1 END) as failed,
                    AVG(CASE WHEN sent_at IS NOT NULL THEN TIMESTAMPDIFF(SECOND, created_at, sent_at) END) as avg_delivery_time
                ')
                ->groupBy('channel')
                ->get();

            return $stats->mapWithKeys(function ($stat) {
                return [$stat->channel => [
                    'total' => $stat->total,
                    'sent' => $stat->sent,
                    'failed' => $stat->failed,
                    'success_rate' => $stat->total > 0 ? round(($stat->sent / $stat->total) * 100, 2) : 0,
                    'avg_delivery_time' => $stat->avg_delivery_time ? round($stat->avg_delivery_time, 2) : null,
                ]];
            })->toArray();

        } catch (\Exception $e) {
            Log::error('Failed to get channel statistics', [
                'from' => $from->toDateString(),
                'to' => $to->toDateString(),
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Tip istatistikleri al
     */
    public function getTypeStatistics(Carbon $from = null, Carbon $to = null): array
    {
        $from = $from ?: Carbon::now()->subDays(30);
        $to = $to ?: Carbon::now();

        try {
            $stats = EloquentNotification::whereBetween('created_at', [$from, $to])
                ->selectRaw('
                    type,
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = "read" THEN 1 END) as read,
                    AVG(CASE WHEN read_at IS NOT NULL AND sent_at IS NOT NULL THEN TIMESTAMPDIFF(SECOND, sent_at, read_at) END) as avg_read_time
                ')
                ->groupBy('type')
                ->get();

            return $stats->mapWithKeys(function ($stat) {
                return [$stat->type => [
                    'total' => $stat->total,
                    'read' => $stat->read,
                    'read_rate' => $stat->total > 0 ? round(($stat->read / $stat->total) * 100, 2) : 0,
                    'avg_read_time' => $stat->avg_read_time ? round($stat->avg_read_time, 2) : null,
                ]];
            })->toArray();

        } catch (\Exception $e) {
            Log::error('Failed to get type statistics', [
                'from' => $from->toDateString(),
                'to' => $to->toDateString(),
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Başarı oranı al
     */
    public function getSuccessRate(Carbon $from = null, Carbon $to = null): float
    {
        $stats = $this->getStatistics($from, $to);
        return $stats['delivery_rate'] ?? 0.0;
    }

    /**
     * Ortalama teslimat süresi al
     */
    public function getAverageDeliveryTime(Carbon $from = null, Carbon $to = null): ?float
    {
        $stats = $this->getStatistics($from, $to);
        return $stats['avg_delivery_time'] ?? null;
    }

    /**
     * Alıcı bildirim sayısı al
     */
    public function getRecipientNotificationCount(RecipientInfo $recipient): int
    {
        $query = EloquentNotification::where('recipient_type', $recipient->getType());

        if ($recipient->getId()) {
            $query->where('recipient_user_id', $recipient->getId());
        }

        return $query->count();
    }

    /**
     * Günlük bildirim limiti kontrol et
     */
    public function checkDailyLimit(RecipientInfo $recipient, int $limit): bool
    {
        $today = Carbon::today();
        $tomorrow = Carbon::tomorrow();

        $query = EloquentNotification::where('recipient_type', $recipient->getType())
            ->whereBetween('created_at', [$today, $tomorrow]);

        if ($recipient->getId()) {
            $query->where('recipient_user_id', $recipient->getId());
        }

        $todayCount = $query->count();

        return $todayCount < $limit;
    }

    /**
     * Arama yap
     */
    public function search(array $criteria, int $limit = 10, int $offset = 0): array
    {
        $query = EloquentNotification::query();

        foreach ($criteria as $field => $value) {
            if ($value !== null) {
                $query->where($field, $value);
            }
        }

        $eloquentModels = $query->orderBy('created_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->mapToDomainArray($eloquentModels);
    }

    /**
     * Toplam sayı al
     */
    public function count(array $criteria = []): int
    {
        $query = EloquentNotification::query();

        foreach ($criteria as $field => $value) {
            if ($value !== null) {
                $query->where($field, $value);
            }
        }

        return $query->count();
    }

    /**
     * Notification cache'ini temizle
     */
    private function clearNotificationCache(Notification $notification): void
    {
        if ($notification->getId()) {
            Cache::forget("notification:{$notification->getId()}");
        }
    }
}
