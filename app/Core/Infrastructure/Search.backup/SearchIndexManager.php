<?php

namespace App\Core\Infrastructure\Search;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * SearchIndexManager
 * Search index management and synchronization service
 */
class SearchIndexManager
{
    protected ElasticsearchService $elasticsearch;
    protected array $config;

    public function __construct(ElasticsearchService $elasticsearch)
    {
        $this->elasticsearch = $elasticsearch;
        $this->config = config('elasticsearch');
    }

    /**
     * Tüm indexleri oluştur
     */
    public function createAllIndices(): bool
    {
        $success = true;

        foreach (array_keys($this->config['indices']) as $indexName) {
            if (!$this->elasticsearch->createOrUpdateIndex($indexName)) {
                $success = false;
                Log::error("Failed to create index: {$indexName}");
            }
        }

        return $success;
    }

    /**
     * Ürünleri indexle
     */
    public function indexProducts(Collection $products = null): bool
    {
        try {
            if ($products === null) {
                $products = Product::with(['category', 'variants', 'attributes'])
                    ->where('status', 'active')
                    ->get();
            }

            $indexName = $this->config['indices']['products']['name'];
            $documents = [];

            foreach ($products as $product) {
                $documents[$product->id] = $this->transformProductForIndex($product);
            }

            if (empty($documents)) {
                Log::info('No products to index');
                return true;
            }

            $success = $this->elasticsearch->bulkIndex($indexName, $documents);
            
            if ($success) {
                Log::info("Successfully indexed {$products->count()} products");
            }

            return $success;
        } catch (\Exception $e) {
            Log::error("Product indexing failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Tek ürünü indexle
     */
    public function indexProduct(Product $product): bool
    {
        try {
            $indexName = $this->config['indices']['products']['name'];
            $document = $this->transformProductForIndex($product);

            return $this->elasticsearch->indexDocument($indexName, $product->id, $document);
        } catch (\Exception $e) {
            Log::error("Single product indexing failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Ürünü indexten sil
     */
    public function removeProduct(int $productId): bool
    {
        try {
            $indexName = $this->config['indices']['products']['name'];
            return $this->elasticsearch->deleteDocument($indexName, $productId);
        } catch (\Exception $e) {
            Log::error("Product removal from index failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Ürünü index için dönüştür
     */
    protected function transformProductForIndex(Product $product): array
    {
        // Kategori bilgilerini al
        $category = $product->category;
        
        // Varyant bilgilerini al
        $variants = $product->variants;
        $defaultVariant = $variants->where('is_default', true)->first() ?? $variants->first();
        
        // Özellik bilgilerini al
        $attributes = [];
        foreach ($product->attributes as $attribute) {
            $attributes[] = [
                'name' => $attribute->name,
                'value' => $attribute->pivot->value ?? $attribute->value
            ];
        }

        // Popülerlik skorunu hesapla
        $popularityScore = $this->calculatePopularityScore($product);

        // Satış sayısını al (örnek - gerçek implementasyonda order_items tablosundan alınabilir)
        $salesCount = $this->getSalesCount($product->id);

        // Görüntülenme sayısını al
        $viewCount = $this->getViewCount($product->id);

        return [
            'id' => $product->id,
            'name' => $product->name,
            'description' => $product->description ?? '',
            'sku' => $product->sku,
            'price' => $defaultVariant ? $defaultVariant->price : $product->price,
            'category_id' => $category ? $category->id : null,
            'category_name' => $category ? $category->name : '',
            'brand' => $product->brand ?? '',
            'tags' => $product->tags ?? '',
            'attributes' => $attributes,
            'status' => $product->status,
            'stock_quantity' => $defaultVariant ? $defaultVariant->stock_quantity : $product->stock_quantity,
            'created_at' => $product->created_at->toISOString(),
            'updated_at' => $product->updated_at->toISOString(),
            'popularity_score' => $popularityScore,
            'sales_count' => $salesCount,
            'view_count' => $viewCount,
            'has_variants' => $variants->count() > 0,
            'variant_count' => $variants->count(),
            'min_price' => $variants->min('price') ?? $product->price,
            'max_price' => $variants->max('price') ?? $product->price,
            'total_stock' => $variants->sum('stock_quantity') ?: $product->stock_quantity,
        ];
    }

    /**
     * Popülerlik skorunu hesapla
     */
    protected function calculatePopularityScore(Product $product): float
    {
        $score = 0.0;

        // Satış sayısı (ağırlık: 40%)
        $salesCount = $this->getSalesCount($product->id);
        $score += ($salesCount * 0.4);

        // Görüntülenme sayısı (ağırlık: 30%)
        $viewCount = $this->getViewCount($product->id);
        $score += ($viewCount * 0.001 * 0.3); // 1000 görüntülenme = 0.3 puan

        // Yorum sayısı (ağırlık: 20%)
        // $reviewCount = $this->getReviewCount($product->id);
        // $score += ($reviewCount * 0.2);

        // Güncellik (ağırlık: 10%)
        $daysSinceCreated = $product->created_at->diffInDays(now());
        $recencyScore = max(0, 100 - $daysSinceCreated) / 100;
        $score += ($recencyScore * 0.1);

        return round($score, 2);
    }

    /**
     * Satış sayısını al
     */
    protected function getSalesCount(int $productId): int
    {
        // Gerçek implementasyonda order_items tablosundan alınacak
        // Şimdilik örnek değer döndürüyoruz
        return rand(0, 100);
    }

    /**
     * Görüntülenme sayısını al
     */
    protected function getViewCount(int $productId): int
    {
        // Gerçek implementasyonda product_views tablosundan alınacak
        // Şimdilik örnek değer döndürüyoruz
        return rand(0, 1000);
    }

    /**
     * Analytics event'ini indexle
     */
    public function indexAnalyticsEvent(array $eventData): bool
    {
        try {
            $indexName = $this->config['indices']['analytics']['name'];
            $eventId = uniqid('event_', true);

            $document = array_merge($eventData, [
                'timestamp' => now()->toISOString(),
                'indexed_at' => now()->toISOString(),
            ]);

            return $this->elasticsearch->indexDocument($indexName, $eventId, $document);
        } catch (\Exception $e) {
            Log::error("Analytics event indexing failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Toplu analytics event'lerini indexle
     */
    public function bulkIndexAnalyticsEvents(array $events): bool
    {
        try {
            $indexName = $this->config['indices']['analytics']['name'];
            $documents = [];

            foreach ($events as $event) {
                $eventId = uniqid('event_', true);
                $documents[$eventId] = array_merge($event, [
                    'timestamp' => $event['timestamp'] ?? now()->toISOString(),
                    'indexed_at' => now()->toISOString(),
                ]);
            }

            return $this->elasticsearch->bulkIndex($indexName, $documents);
        } catch (\Exception $e) {
            Log::error("Bulk analytics events indexing failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Index senkronizasyonu
     */
    public function syncProductIndex(): array
    {
        $stats = [
            'total_products' => 0,
            'indexed_products' => 0,
            'failed_products' => 0,
            'start_time' => now(),
            'end_time' => null,
            'duration' => null,
        ];

        try {
            Log::info('Starting product index synchronization');

            // Tüm aktif ürünleri al
            $products = Product::with(['category', 'variants', 'attributes'])
                ->where('status', 'active')
                ->get();

            $stats['total_products'] = $products->count();

            // Batch'ler halinde indexle
            $batchSize = $this->config['performance']['bulk_size'];
            $batches = $products->chunk($batchSize);

            foreach ($batches as $batch) {
                if ($this->indexProducts($batch)) {
                    $stats['indexed_products'] += $batch->count();
                } else {
                    $stats['failed_products'] += $batch->count();
                }
            }

            $stats['end_time'] = now();
            $stats['duration'] = $stats['start_time']->diffInSeconds($stats['end_time']);

            Log::info('Product index synchronization completed', $stats);

            return $stats;
        } catch (\Exception $e) {
            Log::error("Product index synchronization failed: {$e->getMessage()}");
            $stats['error'] = $e->getMessage();
            return $stats;
        }
    }

    /**
     * Index istatistiklerini al
     */
    public function getIndexStatistics(): array
    {
        $stats = [];

        foreach (array_keys($this->config['indices']) as $indexName) {
            $indexRealName = $this->config['indices'][$indexName]['name'];
            $indexStats = $this->elasticsearch->getIndexStats($indexRealName);
            
            $stats[$indexName] = [
                'name' => $indexRealName,
                'document_count' => $indexStats['indices'][$indexRealName]['total']['docs']['count'] ?? 0,
                'size_in_bytes' => $indexStats['indices'][$indexRealName]['total']['store']['size_in_bytes'] ?? 0,
                'size_human' => $this->formatBytes($indexStats['indices'][$indexRealName]['total']['store']['size_in_bytes'] ?? 0),
            ];
        }

        return $stats;
    }

    /**
     * Cluster durumunu kontrol et
     */
    public function getClusterStatus(): array
    {
        $health = $this->elasticsearch->getClusterHealth();
        
        return [
            'status' => $health['status'] ?? 'unknown',
            'cluster_name' => $health['cluster_name'] ?? 'unknown',
            'number_of_nodes' => $health['number_of_nodes'] ?? 0,
            'number_of_data_nodes' => $health['number_of_data_nodes'] ?? 0,
            'active_primary_shards' => $health['active_primary_shards'] ?? 0,
            'active_shards' => $health['active_shards'] ?? 0,
            'relocating_shards' => $health['relocating_shards'] ?? 0,
            'initializing_shards' => $health['initializing_shards'] ?? 0,
            'unassigned_shards' => $health['unassigned_shards'] ?? 0,
        ];
    }

    /**
     * Byte'ları okunabilir formata çevir
     */
    protected function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Index temizle ve yeniden oluştur
     */
    public function rebuildIndex(string $indexName): bool
    {
        try {
            $indexRealName = $this->config['indices'][$indexName]['name'];
            
            // Eski index'i sil
            $this->elasticsearch->deleteIndex($indexRealName);
            
            // Yeni index oluştur
            if (!$this->elasticsearch->createOrUpdateIndex($indexName)) {
                return false;
            }

            // Eğer products index'i ise, ürünleri yeniden indexle
            if ($indexName === 'products') {
                return $this->indexProducts();
            }

            return true;
        } catch (\Exception $e) {
            Log::error("Index rebuild failed: {$e->getMessage()}");
            return false;
        }
    }
}
