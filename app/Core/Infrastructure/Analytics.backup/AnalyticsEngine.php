<?php

namespace App\Core\Infrastructure\Analytics;

use App\Core\Infrastructure\Search\ElasticsearchService;
use App\Core\Infrastructure\Search\SearchIndexManager;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * AnalyticsEngine
 * Advanced analytics processing engine
 */
class AnalyticsEngine
{
    protected ElasticsearchService $elasticsearch;
    protected SearchIndexManager $indexManager;
    protected array $config;

    public function __construct(
        ElasticsearchService $elasticsearch,
        SearchIndexManager $indexManager
    ) {
        $this->elasticsearch = $elasticsearch;
        $this->indexManager = $indexManager;
        $this->config = config('elasticsearch.analytics');
    }

    /**
     * Event kaydet
     */
    public function recordEvent(string $eventType, array $data): bool
    {
        try {
            $eventData = array_merge($data, [
                'event_type' => $eventType,
                'timestamp' => now()->toISOString(),
                'session_id' => session()->getId(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            // Real-time processing için
            if ($this->config['real_time']) {
                $this->processRealTimeEvent($eventData);
            }

            // Elasticsearch'e kaydet
            return $this->indexManager->indexAnalyticsEvent($eventData);
        } catch (\Exception $e) {
            Log::error("Analytics event recording failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Gerçek zamanlı event işleme
     */
    protected function processRealTimeEvent(array $eventData): void
    {
        $eventType = $eventData['event_type'];
        $cacheKey = "analytics_realtime_{$eventType}";
        
        // Günlük sayaçları güncelle
        $dailyKey = $cacheKey . '_' . now()->format('Y-m-d');
        Cache::increment($dailyKey, 1);
        Cache::expire($dailyKey, 86400); // 24 saat

        // Saatlik sayaçları güncelle
        $hourlyKey = $cacheKey . '_' . now()->format('Y-m-d-H');
        Cache::increment($hourlyKey, 1);
        Cache::expire($hourlyKey, 3600); // 1 saat

        // Event tipine göre özel işlemler
        switch ($eventType) {
            case 'product_view':
                $this->processProductViewEvent($eventData);
                break;
            case 'search_query':
                $this->processSearchEvent($eventData);
                break;
            case 'add_to_cart':
                $this->processCartEvent($eventData);
                break;
            case 'purchase':
                $this->processPurchaseEvent($eventData);
                break;
        }
    }

    /**
     * Ürün görüntüleme event'ini işle
     */
    protected function processProductViewEvent(array $eventData): void
    {
        if (isset($eventData['product_id'])) {
            $productId = $eventData['product_id'];
            
            // Ürün popülerlik skorunu güncelle
            $key = "product_views_{$productId}";
            Cache::increment($key, 1);
            
            // Trending products listesini güncelle
            $trendingKey = 'trending_products_' . now()->format('Y-m-d');
            $trending = Cache::get($trendingKey, []);
            $trending[$productId] = ($trending[$productId] ?? 0) + 1;
            Cache::put($trendingKey, $trending, 86400);
        }
    }

    /**
     * Arama event'ini işle
     */
    protected function processSearchEvent(array $eventData): void
    {
        if (isset($eventData['search_query'])) {
            $query = $eventData['search_query'];
            
            // Popüler arama terimlerini güncelle
            $key = 'popular_searches_' . now()->format('Y-m-d');
            $searches = Cache::get($key, []);
            $searches[$query] = ($searches[$query] ?? 0) + 1;
            Cache::put($key, $searches, 86400);
        }
    }

    /**
     * Sepet event'ini işle
     */
    protected function processCartEvent(array $eventData): void
    {
        if (isset($eventData['product_id'])) {
            $productId = $eventData['product_id'];
            
            // Sepete ekleme oranlarını güncelle
            $key = "cart_additions_{$productId}";
            Cache::increment($key, 1);
        }
    }

    /**
     * Satın alma event'ini işle
     */
    protected function processPurchaseEvent(array $eventData): void
    {
        if (isset($eventData['order_id'])) {
            // Satış metriklerini güncelle
            $revenue = $eventData['revenue'] ?? 0;
            
            $dailyRevenueKey = 'daily_revenue_' . now()->format('Y-m-d');
            Cache::increment($dailyRevenueKey, $revenue);
            
            $dailyOrdersKey = 'daily_orders_' . now()->format('Y-m-d');
            Cache::increment($dailyOrdersKey, 1);
        }
    }

    /**
     * Gerçek zamanlı metrikleri al
     */
    public function getRealTimeMetrics(): array
    {
        $today = now()->format('Y-m-d');
        $currentHour = now()->format('Y-m-d-H');

        return [
            'today' => [
                'page_views' => Cache::get("analytics_realtime_page_view_{$today}", 0),
                'product_views' => Cache::get("analytics_realtime_product_view_{$today}", 0),
                'searches' => Cache::get("analytics_realtime_search_query_{$today}", 0),
                'cart_additions' => Cache::get("analytics_realtime_add_to_cart_{$today}", 0),
                'purchases' => Cache::get("analytics_realtime_purchase_{$today}", 0),
                'revenue' => Cache::get("daily_revenue_{$today}", 0),
            ],
            'current_hour' => [
                'page_views' => Cache::get("analytics_realtime_page_view_{$currentHour}", 0),
                'product_views' => Cache::get("analytics_realtime_product_view_{$currentHour}", 0),
                'searches' => Cache::get("analytics_realtime_search_query_{$currentHour}", 0),
                'cart_additions' => Cache::get("analytics_realtime_add_to_cart_{$currentHour}", 0),
                'purchases' => Cache::get("analytics_realtime_purchase_{$currentHour}", 0),
            ],
            'trending_products' => $this->getTrendingProducts(),
            'popular_searches' => $this->getPopularSearches(),
        ];
    }

    /**
     * Trend olan ürünleri al
     */
    public function getTrendingProducts(int $limit = 10): array
    {
        $key = 'trending_products_' . now()->format('Y-m-d');
        $trending = Cache::get($key, []);
        
        arsort($trending);
        $topProducts = array_slice($trending, 0, $limit, true);
        
        // Ürün bilgilerini ekle
        $products = [];
        foreach ($topProducts as $productId => $viewCount) {
            $product = DB::table('products')
                ->select('id', 'name', 'price', 'sku')
                ->where('id', $productId)
                ->first();
                
            if ($product) {
                $products[] = [
                    'product' => $product,
                    'view_count' => $viewCount,
                ];
            }
        }
        
        return $products;
    }

    /**
     * Popüler arama terimlerini al
     */
    public function getPopularSearches(int $limit = 10): array
    {
        $key = 'popular_searches_' . now()->format('Y-m-d');
        $searches = Cache::get($key, []);
        
        arsort($searches);
        return array_slice($searches, 0, $limit, true);
    }

    /**
     * Conversion funnel analizi
     */
    public function getConversionFunnel(Carbon $startDate, Carbon $endDate): array
    {
        $cacheKey = "conversion_funnel_" . $startDate->format('Y-m-d') . "_" . $endDate->format('Y-m-d');
        
        return Cache::remember($cacheKey, 3600, function () use ($startDate, $endDate) {
            $searchParams = [
                'index' => config('elasticsearch.indices.analytics.name'),
                'body' => [
                    'size' => 0,
                    'query' => [
                        'bool' => [
                            'filter' => [
                                [
                                    'range' => [
                                        'timestamp' => [
                                            'gte' => $startDate->toISOString(),
                                            'lte' => $endDate->toISOString(),
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'aggs' => [
                        'funnel_steps' => [
                            'filters' => [
                                'filters' => [
                                    'page_views' => [
                                        'term' => ['event_type' => 'page_view']
                                    ],
                                    'product_views' => [
                                        'term' => ['event_type' => 'product_view']
                                    ],
                                    'cart_additions' => [
                                        'term' => ['event_type' => 'add_to_cart']
                                    ],
                                    'checkouts' => [
                                        'term' => ['event_type' => 'checkout_started']
                                    ],
                                    'purchases' => [
                                        'term' => ['event_type' => 'purchase']
                                    ],
                                ]
                            ]
                        ]
                    ]
                ]
            ];

            try {
                $response = $this->elasticsearch->getClient()->search($searchParams);
                $buckets = $response['aggregations']['funnel_steps']['buckets'] ?? [];

                $funnel = [
                    'page_views' => $buckets['page_views']['doc_count'] ?? 0,
                    'product_views' => $buckets['product_views']['doc_count'] ?? 0,
                    'cart_additions' => $buckets['cart_additions']['doc_count'] ?? 0,
                    'checkouts' => $buckets['checkouts']['doc_count'] ?? 0,
                    'purchases' => $buckets['purchases']['doc_count'] ?? 0,
                ];

                // Conversion oranlarını hesapla
                $funnel['conversion_rates'] = [
                    'view_to_product' => $funnel['page_views'] > 0 ? 
                        round(($funnel['product_views'] / $funnel['page_views']) * 100, 2) : 0,
                    'product_to_cart' => $funnel['product_views'] > 0 ? 
                        round(($funnel['cart_additions'] / $funnel['product_views']) * 100, 2) : 0,
                    'cart_to_checkout' => $funnel['cart_additions'] > 0 ? 
                        round(($funnel['checkouts'] / $funnel['cart_additions']) * 100, 2) : 0,
                    'checkout_to_purchase' => $funnel['checkouts'] > 0 ? 
                        round(($funnel['purchases'] / $funnel['checkouts']) * 100, 2) : 0,
                    'overall_conversion' => $funnel['page_views'] > 0 ? 
                        round(($funnel['purchases'] / $funnel['page_views']) * 100, 2) : 0,
                ];

                return $funnel;
            } catch (\Exception $e) {
                Log::error("Conversion funnel analysis failed: {$e->getMessage()}");
                return [];
            }
        });
    }

    /**
     * Ürün performans analizi
     */
    public function getProductPerformance(int $productId, Carbon $startDate, Carbon $endDate): array
    {
        $cacheKey = "product_performance_{$productId}_" . $startDate->format('Y-m-d') . "_" . $endDate->format('Y-m-d');
        
        return Cache::remember($cacheKey, 1800, function () use ($productId, $startDate, $endDate) {
            $searchParams = [
                'index' => config('elasticsearch.indices.analytics.name'),
                'body' => [
                    'size' => 0,
                    'query' => [
                        'bool' => [
                            'filter' => [
                                [
                                    'term' => ['product_id' => $productId]
                                ],
                                [
                                    'range' => [
                                        'timestamp' => [
                                            'gte' => $startDate->toISOString(),
                                            'lte' => $endDate->toISOString(),
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'aggs' => [
                        'events_by_type' => [
                            'terms' => [
                                'field' => 'event_type',
                                'size' => 20
                            ]
                        ],
                        'daily_views' => [
                            'filter' => [
                                'term' => ['event_type' => 'product_view']
                            ],
                            'aggs' => [
                                'by_day' => [
                                    'date_histogram' => [
                                        'field' => 'timestamp',
                                        'calendar_interval' => 'day'
                                    ]
                                ]
                            ]
                        ],
                        'total_revenue' => [
                            'filter' => [
                                'term' => ['event_type' => 'purchase']
                            ],
                            'aggs' => [
                                'revenue_sum' => [
                                    'sum' => ['field' => 'revenue']
                                ]
                            ]
                        ]
                    ]
                ]
            ];

            try {
                $response = $this->elasticsearch->getClient()->search($searchParams);
                
                $eventTypes = [];
                foreach ($response['aggregations']['events_by_type']['buckets'] ?? [] as $bucket) {
                    $eventTypes[$bucket['key']] = $bucket['doc_count'];
                }

                $dailyViews = [];
                foreach ($response['aggregations']['daily_views']['by_day']['buckets'] ?? [] as $bucket) {
                    $dailyViews[$bucket['key_as_string']] = $bucket['doc_count'];
                }

                $totalRevenue = $response['aggregations']['total_revenue']['revenue_sum']['value'] ?? 0;

                return [
                    'product_id' => $productId,
                    'period' => [
                        'start' => $startDate->toDateString(),
                        'end' => $endDate->toDateString(),
                    ],
                    'events' => $eventTypes,
                    'daily_views' => $dailyViews,
                    'total_revenue' => $totalRevenue,
                    'conversion_rate' => isset($eventTypes['product_view'], $eventTypes['purchase']) && $eventTypes['product_view'] > 0 ?
                        round(($eventTypes['purchase'] / $eventTypes['product_view']) * 100, 2) : 0,
                ];
            } catch (\Exception $e) {
                Log::error("Product performance analysis failed: {$e->getMessage()}");
                return [];
            }
        });
    }

    /**
     * Arama analitikleri
     */
    public function getSearchAnalytics(Carbon $startDate, Carbon $endDate): array
    {
        $cacheKey = "search_analytics_" . $startDate->format('Y-m-d') . "_" . $endDate->format('Y-m-d');
        
        return Cache::remember($cacheKey, 1800, function () use ($startDate, $endDate) {
            $searchParams = [
                'index' => config('elasticsearch.indices.analytics.name'),
                'body' => [
                    'size' => 0,
                    'query' => [
                        'bool' => [
                            'filter' => [
                                [
                                    'term' => ['event_type' => 'search_query']
                                ],
                                [
                                    'range' => [
                                        'timestamp' => [
                                            'gte' => $startDate->toISOString(),
                                            'lte' => $endDate->toISOString(),
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'aggs' => [
                        'top_searches' => [
                            'terms' => [
                                'field' => 'search_query.keyword',
                                'size' => 20
                            ]
                        ],
                        'zero_results' => [
                            'filter' => [
                                'term' => ['results_count' => 0]
                            ]
                        ],
                        'avg_results' => [
                            'avg' => ['field' => 'results_count']
                        ],
                        'daily_searches' => [
                            'date_histogram' => [
                                'field' => 'timestamp',
                                'calendar_interval' => 'day'
                            ]
                        ]
                    ]
                ]
            ];

            try {
                $response = $this->elasticsearch->getClient()->search($searchParams);
                
                $topSearches = [];
                foreach ($response['aggregations']['top_searches']['buckets'] ?? [] as $bucket) {
                    $topSearches[] = [
                        'query' => $bucket['key'],
                        'count' => $bucket['doc_count']
                    ];
                }

                $dailySearches = [];
                foreach ($response['aggregations']['daily_searches']['buckets'] ?? [] as $bucket) {
                    $dailySearches[$bucket['key_as_string']] = $bucket['doc_count'];
                }

                return [
                    'period' => [
                        'start' => $startDate->toDateString(),
                        'end' => $endDate->toDateString(),
                    ],
                    'total_searches' => $response['hits']['total']['value'] ?? 0,
                    'zero_results_count' => $response['aggregations']['zero_results']['doc_count'] ?? 0,
                    'average_results' => round($response['aggregations']['avg_results']['value'] ?? 0, 2),
                    'top_searches' => $topSearches,
                    'daily_searches' => $dailySearches,
                    'zero_results_rate' => $response['hits']['total']['value'] > 0 ?
                        round((($response['aggregations']['zero_results']['doc_count'] ?? 0) / $response['hits']['total']['value']) * 100, 2) : 0,
                ];
            } catch (\Exception $e) {
                Log::error("Search analytics failed: {$e->getMessage()}");
                return [];
            }
        });
    }

    /**
     * Dashboard özet metrikleri
     */
    public function getDashboardSummary(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'real_time' => $this->getRealTimeMetrics(),
            'conversion_funnel' => $this->getConversionFunnel($startDate, $endDate),
            'search_analytics' => $this->getSearchAnalytics($startDate, $endDate),
            'trending_products' => $this->getTrendingProducts(5),
            'popular_searches' => $this->getPopularSearches(5),
        ];
    }
}
