<?php

namespace App\Core\Infrastructure\Analytics;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;

/**
 * RealTimeProcessor
 * Real-time analytics data processing service
 */
class RealTimeProcessor
{
    protected array $config;
    protected string $redisPrefix;

    public function __construct()
    {
        $this->config = config('elasticsearch.analytics');
        $this->redisPrefix = 'analytics:realtime:';
    }

    /**
     * Gerçek zamanlı event işle
     */
    public function processEvent(array $eventData): void
    {
        try {
            $eventType = $eventData['event_type'];
            $timestamp = Carbon::parse($eventData['timestamp'] ?? now());

            // Zaman bazlı metrikleri güncelle
            $this->updateTimeBasedMetrics($eventType, $timestamp);

            // Event tipine göre özel işlemler
            $this->processSpecificEvent($eventType, $eventData, $timestamp);

            // Genel sayaçları güncelle
            $this->updateGeneralCounters($eventType, $timestamp);

            Log::debug("Real-time event processed: {$eventType}");
        } catch (\Exception $e) {
            Log::error("Real-time event processing failed: {$e->getMessage()}");
        }
    }

    /**
     * Zaman bazlı metrikleri güncelle
     */
    protected function updateTimeBasedMetrics(string $eventType, Carbon $timestamp): void
    {
        $keys = [
            // Günlük
            $this->redisPrefix . "daily:{$eventType}:" . $timestamp->format('Y-m-d'),
            // Saatlik
            $this->redisPrefix . "hourly:{$eventType}:" . $timestamp->format('Y-m-d:H'),
            // Dakikalık (son 60 dakika için)
            $this->redisPrefix . "minutely:{$eventType}:" . $timestamp->format('Y-m-d:H:i'),
        ];

        foreach ($keys as $key) {
            Redis::incr($key);
            
            // TTL ayarla
            if (str_contains($key, 'daily')) {
                Redis::expire($key, 86400 * 7); // 7 gün
            } elseif (str_contains($key, 'hourly')) {
                Redis::expire($key, 3600 * 24); // 24 saat
            } else {
                Redis::expire($key, 3600); // 1 saat
            }
        }
    }

    /**
     * Event tipine göre özel işlemler
     */
    protected function processSpecificEvent(string $eventType, array $eventData, Carbon $timestamp): void
    {
        switch ($eventType) {
            case 'product_view':
                $this->processProductView($eventData, $timestamp);
                break;
            case 'search_query':
                $this->processSearchQuery($eventData, $timestamp);
                break;
            case 'add_to_cart':
                $this->processAddToCart($eventData, $timestamp);
                break;
            case 'purchase':
                $this->processPurchase($eventData, $timestamp);
                break;
            case 'user_registration':
                $this->processUserRegistration($eventData, $timestamp);
                break;
        }
    }

    /**
     * Ürün görüntüleme işle
     */
    protected function processProductView(array $eventData, Carbon $timestamp): void
    {
        if (!isset($eventData['product_id'])) {
            return;
        }

        $productId = $eventData['product_id'];
        $dateKey = $timestamp->format('Y-m-d');

        // Ürün bazlı sayaçlar
        $productKey = $this->redisPrefix . "product_views:{$productId}:{$dateKey}";
        Redis::incr($productKey);
        Redis::expire($productKey, 86400 * 30); // 30 gün

        // Trending products (son 24 saat)
        $trendingKey = $this->redisPrefix . 'trending_products';
        Redis::zincrby($trendingKey, 1, $productId);
        Redis::expire($trendingKey, 86400);

        // Kategori bazlı görüntülemeler
        if (isset($eventData['category_id'])) {
            $categoryKey = $this->redisPrefix . "category_views:{$eventData['category_id']}:{$dateKey}";
            Redis::incr($categoryKey);
            Redis::expire($categoryKey, 86400 * 7);
        }

        // Kullanıcı bazlı aktivite
        if (isset($eventData['user_id'])) {
            $userKey = $this->redisPrefix . "user_activity:{$eventData['user_id']}:{$dateKey}";
            Redis::sadd($userKey, $productId);
            Redis::expire($userKey, 86400 * 7);
        }
    }

    /**
     * Arama sorgusu işle
     */
    protected function processSearchQuery(array $eventData, Carbon $timestamp): void
    {
        if (!isset($eventData['search_query'])) {
            return;
        }

        $query = strtolower(trim($eventData['search_query']));
        $dateKey = $timestamp->format('Y-m-d');

        // Popüler aramalar
        $popularKey = $this->redisPrefix . "popular_searches:{$dateKey}";
        Redis::zincrby($popularKey, 1, $query);
        Redis::expire($popularKey, 86400 * 7);

        // Sonuç sayısı tracking
        $resultsCount = $eventData['results_count'] ?? 0;
        if ($resultsCount === 0) {
            $zeroResultsKey = $this->redisPrefix . "zero_results:{$dateKey}";
            Redis::sadd($zeroResultsKey, $query);
            Redis::expire($zeroResultsKey, 86400 * 7);
        }

        // Arama performansı
        $performanceKey = $this->redisPrefix . "search_performance:{$dateKey}";
        Redis::hset($performanceKey, $query, $resultsCount);
        Redis::expire($performanceKey, 86400 * 7);
    }

    /**
     * Sepete ekleme işle
     */
    protected function processAddToCart(array $eventData, Carbon $timestamp): void
    {
        if (!isset($eventData['product_id'])) {
            return;
        }

        $productId = $eventData['product_id'];
        $dateKey = $timestamp->format('Y-m-d');

        // Ürün bazlı sepete ekleme
        $cartKey = $this->redisPrefix . "cart_additions:{$productId}:{$dateKey}";
        Redis::incr($cartKey);
        Redis::expire($cartKey, 86400 * 30);

        // Conversion tracking için
        if (isset($eventData['user_id'])) {
            $conversionKey = $this->redisPrefix . "conversion_tracking:{$eventData['user_id']}";
            Redis::hset($conversionKey, 'cart_addition', $timestamp->timestamp);
            Redis::expire($conversionKey, 3600 * 24); // 24 saat
        }

        // Miktar tracking
        $quantity = $eventData['quantity'] ?? 1;
        $quantityKey = $this->redisPrefix . "cart_quantity:{$dateKey}";
        Redis::incrby($quantityKey, $quantity);
        Redis::expire($quantityKey, 86400 * 7);
    }

    /**
     * Satın alma işle
     */
    protected function processPurchase(array $eventData, Carbon $timestamp): void
    {
        $dateKey = $timestamp->format('Y-m-d');
        $hourKey = $timestamp->format('Y-m-d:H');

        // Gelir tracking
        $revenue = $eventData['revenue'] ?? 0;
        if ($revenue > 0) {
            $revenueKey = $this->redisPrefix . "revenue:{$dateKey}";
            Redis::incrbyfloat($revenueKey, $revenue);
            Redis::expire($revenueKey, 86400 * 30);

            // Saatlik gelir
            $hourlyRevenueKey = $this->redisPrefix . "hourly_revenue:{$hourKey}";
            Redis::incrbyfloat($hourlyRevenueKey, $revenue);
            Redis::expire($hourlyRevenueKey, 86400);
        }

        // Sipariş sayısı
        $ordersKey = $this->redisPrefix . "orders:{$dateKey}";
        Redis::incr($ordersKey);
        Redis::expire($ordersKey, 86400 * 30);

        // Ürün bazlı satışlar
        if (isset($eventData['products'])) {
            foreach ($eventData['products'] as $product) {
                $productSalesKey = $this->redisPrefix . "product_sales:{$product['id']}:{$dateKey}";
                Redis::incrby($productSalesKey, $product['quantity'] ?? 1);
                Redis::expire($productSalesKey, 86400 * 30);
            }
        }

        // Conversion tracking tamamla
        if (isset($eventData['user_id'])) {
            $conversionKey = $this->redisPrefix . "conversion_tracking:{$eventData['user_id']}";
            Redis::hset($conversionKey, 'purchase', $timestamp->timestamp);
            Redis::expire($conversionKey, 3600 * 24);
        }
    }

    /**
     * Kullanıcı kaydı işle
     */
    protected function processUserRegistration(array $eventData, Carbon $timestamp): void
    {
        $dateKey = $timestamp->format('Y-m-d');

        // Günlük kayıt sayısı
        $registrationKey = $this->redisPrefix . "registrations:{$dateKey}";
        Redis::incr($registrationKey);
        Redis::expire($registrationKey, 86400 * 30);

        // Kaynak tracking
        if (isset($eventData['source'])) {
            $sourceKey = $this->redisPrefix . "registration_sources:{$dateKey}";
            Redis::zincrby($sourceKey, 1, $eventData['source']);
            Redis::expire($sourceKey, 86400 * 30);
        }
    }

    /**
     * Genel sayaçları güncelle
     */
    protected function updateGeneralCounters(string $eventType, Carbon $timestamp): void
    {
        $dateKey = $timestamp->format('Y-m-d');

        // Toplam event sayısı
        $totalKey = $this->redisPrefix . "total_events:{$dateKey}";
        Redis::incr($totalKey);
        Redis::expire($totalKey, 86400 * 7);

        // Event tipi bazlı sayaçlar
        $typeKey = $this->redisPrefix . "event_types:{$dateKey}";
        Redis::zincrby($typeKey, 1, $eventType);
        Redis::expire($typeKey, 86400 * 7);

        // Aktif kullanıcı sayısı (unique sessions)
        $sessionId = session()->getId();
        if ($sessionId) {
            $activeUsersKey = $this->redisPrefix . "active_users:{$dateKey}";
            Redis::sadd($activeUsersKey, $sessionId);
            Redis::expire($activeUsersKey, 86400);
        }
    }

    /**
     * Gerçek zamanlı metrikleri al
     */
    public function getRealTimeMetrics(): array
    {
        $today = now()->format('Y-m-d');
        $currentHour = now()->format('Y-m-d:H');

        return [
            'current_metrics' => [
                'active_users' => Redis::scard($this->redisPrefix . "active_users:{$today}"),
                'page_views' => Redis::get($this->redisPrefix . "daily:page_view:{$today}") ?: 0,
                'product_views' => Redis::get($this->redisPrefix . "daily:product_view:{$today}") ?: 0,
                'searches' => Redis::get($this->redisPrefix . "daily:search_query:{$today}") ?: 0,
                'cart_additions' => Redis::get($this->redisPrefix . "daily:add_to_cart:{$today}") ?: 0,
                'purchases' => Redis::get($this->redisPrefix . "daily:purchase:{$today}") ?: 0,
                'revenue' => Redis::get($this->redisPrefix . "revenue:{$today}") ?: 0,
            ],
            'hourly_metrics' => [
                'page_views' => Redis::get($this->redisPrefix . "hourly:page_view:{$currentHour}") ?: 0,
                'product_views' => Redis::get($this->redisPrefix . "hourly:product_view:{$currentHour}") ?: 0,
                'searches' => Redis::get($this->redisPrefix . "hourly:search_query:{$currentHour}") ?: 0,
                'cart_additions' => Redis::get($this->redisPrefix . "hourly:add_to_cart:{$currentHour}") ?: 0,
                'purchases' => Redis::get($this->redisPrefix . "hourly:purchase:{$currentHour}") ?: 0,
                'revenue' => Redis::get($this->redisPrefix . "hourly_revenue:{$currentHour}") ?: 0,
            ],
            'trending' => [
                'products' => $this->getTrendingProducts(),
                'searches' => $this->getPopularSearches(),
            ],
        ];
    }

    /**
     * Trend olan ürünleri al
     */
    public function getTrendingProducts(int $limit = 10): array
    {
        $key = $this->redisPrefix . 'trending_products';
        $products = Redis::zrevrange($key, 0, $limit - 1, 'WITHSCORES');
        
        $result = [];
        for ($i = 0; $i < count($products); $i += 2) {
            $result[] = [
                'product_id' => $products[$i],
                'score' => $products[$i + 1],
            ];
        }
        
        return $result;
    }

    /**
     * Popüler aramaları al
     */
    public function getPopularSearches(int $limit = 10): array
    {
        $today = now()->format('Y-m-d');
        $key = $this->redisPrefix . "popular_searches:{$today}";
        $searches = Redis::zrevrange($key, 0, $limit - 1, 'WITHSCORES');
        
        $result = [];
        for ($i = 0; $i < count($searches); $i += 2) {
            $result[] = [
                'query' => $searches[$i],
                'count' => $searches[$i + 1],
            ];
        }
        
        return $result;
    }

    /**
     * Son dakika metriklerini al
     */
    public function getLastMinuteMetrics(): array
    {
        $minutes = [];
        $now = now();
        
        for ($i = 0; $i < 10; $i++) {
            $minute = $now->copy()->subMinutes($i);
            $minuteKey = $minute->format('Y-m-d:H:i');
            
            $minutes[] = [
                'time' => $minuteKey,
                'page_views' => Redis::get($this->redisPrefix . "minutely:page_view:{$minuteKey}") ?: 0,
                'product_views' => Redis::get($this->redisPrefix . "minutely:product_view:{$minuteKey}") ?: 0,
                'searches' => Redis::get($this->redisPrefix . "minutely:search_query:{$minuteKey}") ?: 0,
            ];
        }
        
        return array_reverse($minutes);
    }

    /**
     * Cache temizle
     */
    public function clearCache(string $pattern = null): int
    {
        $pattern = $pattern ?: $this->redisPrefix . '*';
        $keys = Redis::keys($pattern);
        
        if (empty($keys)) {
            return 0;
        }
        
        return Redis::del($keys);
    }
}
