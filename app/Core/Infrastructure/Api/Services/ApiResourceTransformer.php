<?php

namespace App\Core\Infrastructure\Api\Services;

use App\Core\Infrastructure\Api\Contracts\ApiTransformerInterface;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Carbon\Carbon;

/**
 * ApiResourceTransformer
 * Enhanced API resource transformation service
 */
class ApiResourceTransformer implements ApiTransformerInterface
{
    private array $includes = [];
    private array $excludes = [];
    private array $context = [];
    private string $transformType = 'default';

    /**
     * Data'yı transform et
     */
    public function transform($data): array
    {
        if (!$this->canTransform($data)) {
            return is_array($data) ? $data : [];
        }

        if ($data instanceof JsonResource) {
            return $this->transformResource($data);
        }

        if ($data instanceof ResourceCollection) {
            return $this->transformResourceCollection($data);
        }

        if ($data instanceof LengthAwarePaginator) {
            return $this->transformPaginator($data);
        }

        if ($data instanceof Collection || is_array($data)) {
            return $this->transformCollection($data);
        }

        if (is_object($data)) {
            return $this->transformObject($data);
        }

        // Scalar değerler için array wrapper
        $scalarResult = $this->transformScalar($data);
        return is_array($scalarResult) ? $scalarResult : ['value' => $scalarResult];
    }

    /**
     * Collection'ı transform et
     */
    public function transformCollection($collection): array
    {
        if ($collection instanceof Collection) {
            return $collection->map(function ($item) {
                return $this->transform($item);
            })->toArray();
        }

        if (is_array($collection)) {
            return array_map(function ($item) {
                return $this->transform($item);
            }, $collection);
        }

        return [];
    }

    /**
     * Pagination data'sını transform et
     */
    public function transformPagination($paginator): array
    {
        if (!$paginator instanceof LengthAwarePaginator) {
            return [];
        }

        return [
            'current_page' => $paginator->currentPage(),
            'last_page' => $paginator->lastPage(),
            'per_page' => $paginator->perPage(),
            'total' => $paginator->total(),
            'from' => $paginator->firstItem(),
            'to' => $paginator->lastItem(),
            'has_more_pages' => $paginator->hasMorePages(),
            'path' => $paginator->path(),
            'first_page_url' => $paginator->url(1),
            'last_page_url' => $paginator->url($paginator->lastPage()),
            'next_page_url' => $paginator->nextPageUrl(),
            'prev_page_url' => $paginator->previousPageUrl(),
        ];
    }

    /**
     * Meta data'yı transform et
     */
    public function transformMeta(array $meta = []): array
    {
        $defaultMeta = [
            'timestamp' => Carbon::now()->toISOString(),
            'timezone' => config('app.timezone', 'UTC'),
            'locale' => app()->getLocale(),
        ];

        if (!empty($this->context)) {
            $defaultMeta['context'] = $this->context;
        }

        return array_merge($defaultMeta, $meta);
    }

    /**
     * Error data'sını transform et
     */
    public function transformError(string $message, array $errors = [], array $meta = []): array
    {
        $errorData = [
            'message' => $message,
            'errors' => $this->transformErrors($errors),
        ];

        if (!empty($meta)) {
            $errorData['meta'] = $this->transformMeta($meta);
        }

        return $errorData;
    }

    /**
     * Success data'sını transform et
     */
    public function transformSuccess($data, string $message = '', array $meta = []): array
    {
        $successData = [
            'data' => $this->transform($data),
        ];

        if (!empty($message)) {
            $successData['message'] = $message;
        }

        if (!empty($meta)) {
            $successData['meta'] = $this->transformMeta($meta);
        }

        return $successData;
    }

    /**
     * Include'ları ayarla
     */
    public function setIncludes(array $includes): self
    {
        $this->includes = $includes;
        return $this;
    }

    /**
     * Exclude'ları ayarla
     */
    public function setExcludes(array $excludes): self
    {
        $this->excludes = $excludes;
        return $this;
    }

    /**
     * Transform context'ini ayarla
     */
    public function setContext(array $context): self
    {
        $this->context = $context;
        return $this;
    }

    /**
     * Transform edilebilir mi kontrol et
     */
    public function canTransform($data): bool
    {
        return $data !== null;
    }

    /**
     * Transform type'ını al
     */
    public function getTransformType(): string
    {
        return $this->transformType;
    }

    /**
     * Transform type'ını ayarla
     */
    public function setTransformType(string $type): self
    {
        $this->transformType = $type;
        return $this;
    }

    /**
     * JsonResource'u transform et
     */
    private function transformResource(JsonResource $resource): array
    {
        $data = $resource->toArray(request());
        return $this->applyIncludesExcludes($data);
    }

    /**
     * ResourceCollection'ı transform et
     */
    private function transformResourceCollection(ResourceCollection $collection): array
    {
        $data = $collection->toArray(request());

        if (isset($data['data'])) {
            $data['data'] = array_map(function ($item) {
                return $this->applyIncludesExcludes($item);
            }, $data['data']);
        }

        return $data;
    }

    /**
     * Paginator'ı transform et
     */
    private function transformPaginator(LengthAwarePaginator $paginator): array
    {
        $items = $paginator->items();
        $transformedItems = array_map(function ($item) {
            return $this->transform($item);
        }, $items);

        return [
            'data' => $transformedItems,
            'pagination' => $this->transformPagination($paginator),
        ];
    }

    /**
     * Object'i transform et
     */
    private function transformObject($object): array
    {
        // DateTime objects için özel handling
        if ($object instanceof \DateTime || $object instanceof Carbon) {
            $scalarValue = $this->transformScalar($object);
            return ['value' => $scalarValue];
        }

        if (method_exists($object, 'toArray')) {
            $data = $object->toArray();
        } elseif (method_exists($object, 'jsonSerialize')) {
            $data = $object->jsonSerialize();
        } else {
            $data = (array) $object;
        }

        return $this->applyIncludesExcludes($data);
    }

    /**
     * Scalar value'yu transform et
     */
    private function transformScalar($value): mixed
    {
        if ($value instanceof Carbon) {
            return $value->toISOString();
        }

        if ($value instanceof \DateTime) {
            return $value->format('c');
        }

        return $value;
    }

    /**
     * Error'ları transform et
     */
    private function transformErrors(array $errors): array
    {
        $transformed = [];

        foreach ($errors as $field => $messages) {
            if (is_array($messages)) {
                $transformed[$field] = $messages;
            } else {
                $transformed[$field] = [$messages];
            }
        }

        return $transformed;
    }

    /**
     * Include/Exclude kurallarını uygula
     */
    private function applyIncludesExcludes(array $data): array
    {
        // Exclude'ları uygula
        if (!empty($this->excludes)) {
            foreach ($this->excludes as $exclude) {
                if (isset($data[$exclude])) {
                    unset($data[$exclude]);
                }
            }
        }

        // Include'ları uygula (eğer belirtilmişse sadece bunları dahil et)
        if (!empty($this->includes)) {
            $filteredData = [];
            foreach ($this->includes as $include) {
                if (isset($data[$include])) {
                    $filteredData[$include] = $data[$include];
                }
            }
            return $filteredData;
        }

        return $data;
    }

    /**
     * Include'ları al
     */
    public function getIncludes(): array
    {
        return $this->includes;
    }

    /**
     * Exclude'ları al
     */
    public function getExcludes(): array
    {
        return $this->excludes;
    }

    /**
     * Context'i al
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Nested data'yı transform et
     */
    public function transformNested($data, string $key): array
    {
        if (!isset($data[$key])) {
            return [];
        }

        return $this->transform($data[$key]);
    }

    /**
     * Conditional transformation
     */
    public function transformWhen(bool $condition, $data, $default = null): mixed
    {
        if ($condition) {
            return $this->transform($data);
        }

        return $default;
    }

    /**
     * Transform with custom callback
     */
    public function transformWith(callable $callback, $data): mixed
    {
        return $callback($this->transform($data));
    }
}
