<?php

namespace App\Core\Infrastructure\Search;

use Elastic\Elasticsearch\Client;
use Elastic\Elasticsearch\ClientBuilder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

/**
 * ElasticsearchService
 * Advanced Elasticsearch integration service
 */
class ElasticsearchService
{
    protected Client $client;
    protected array $config;
    protected string $defaultIndex;

    public function __construct()
    {
        $this->config = config('elasticsearch');
        $this->client = $this->createClient();
        $this->defaultIndex = $this->config['indices']['products']['name'];
    }

    /**
     * Elasticsearch client oluştur
     */
    protected function createClient(): Client
    {
        $connectionConfig = $this->config['connections'][$this->config['default']];
        
        $clientBuilder = ClientBuilder::create()
            ->setHosts($connectionConfig['hosts'])
            ->setRetries($connectionConfig['retries']);

        // Authentication
        if (!empty($connectionConfig['username']) && !empty($connectionConfig['password'])) {
            $clientBuilder->setBasicAuthentication(
                $connectionConfig['username'],
                $connectionConfig['password']
            );
        }

        if (!empty($connectionConfig['api_key'])) {
            $clientBuilder->setApiKey($connectionConfig['api_key']);
        }

        if (!empty($connectionConfig['cloud_id'])) {
            $clientBuilder->setElasticCloudId($connectionConfig['cloud_id']);
        }

        // SSL verification
        if (!$connectionConfig['ssl_verification']) {
            $clientBuilder->setSSLVerification(false);
        }

        // Logging
        if ($this->config['logging']['enabled']) {
            $clientBuilder->setLogger(Log::channel($this->config['logging']['channel']));
        }

        return $clientBuilder->build();
    }

    /**
     * Index oluştur veya güncelle
     */
    public function createOrUpdateIndex(string $indexName): bool
    {
        try {
            $indexConfig = $this->config['indices'][$indexName] ?? null;
            
            if (!$indexConfig) {
                throw new \InvalidArgumentException("Index configuration not found: {$indexName}");
            }

            $indexRealName = $indexConfig['name'];

            // Index var mı kontrol et
            if ($this->client->indices()->exists(['index' => $indexRealName])) {
                // Mapping güncelle
                $this->client->indices()->putMapping([
                    'index' => $indexRealName,
                    'body' => $indexConfig['mappings']
                ]);
                
                Log::info("Elasticsearch index mapping updated: {$indexRealName}");
            } else {
                // Index oluştur
                $this->client->indices()->create([
                    'index' => $indexRealName,
                    'body' => [
                        'settings' => $indexConfig['settings'],
                        'mappings' => $indexConfig['mappings']
                    ]
                ]);
                
                Log::info("Elasticsearch index created: {$indexRealName}");
            }

            return true;
        } catch (\Exception $e) {
            Log::error("Elasticsearch index creation failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Gelişmiş arama
     */
    public function search(array $params): array
    {
        $cacheKey = 'elasticsearch_search_' . md5(serialize($params));
        
        if ($this->config['performance']['cache_enabled']) {
            $cached = Cache::get($cacheKey);
            if ($cached) {
                return $cached;
            }
        }

        try {
            $searchParams = $this->buildSearchParams($params);
            $response = $this->client->search($searchParams);
            
            $result = $this->formatSearchResponse($response);
            
            if ($this->config['performance']['cache_enabled']) {
                Cache::put($cacheKey, $result, $this->config['performance']['cache_ttl']);
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error("Elasticsearch search failed: {$e->getMessage()}");
            return [
                'hits' => [],
                'total' => 0,
                'aggregations' => [],
                'suggestions' => [],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Arama parametrelerini oluştur
     */
    protected function buildSearchParams(array $params): array
    {
        $index = $params['index'] ?? $this->defaultIndex;
        $query = $params['query'] ?? '';
        $filters = $params['filters'] ?? [];
        $sort = $params['sort'] ?? [];
        $size = min($params['size'] ?? $this->config['search']['default_size'], $this->config['search']['max_size']);
        $from = $params['from'] ?? 0;

        $searchParams = [
            'index' => $index,
            'body' => [
                'size' => $size,
                'from' => $from,
                'track_total_hits' => $this->config['search']['track_total_hits'],
                'timeout' => $this->config['search']['timeout'],
            ]
        ];

        // Query oluştur
        if (!empty($query)) {
            $searchParams['body']['query'] = $this->buildQuery($query, $filters);
        } else {
            $searchParams['body']['query'] = $this->buildFilterOnlyQuery($filters);
        }

        // Sorting
        if (!empty($sort)) {
            $searchParams['body']['sort'] = $this->buildSort($sort);
        }

        // Highlighting
        if ($this->config['search']['highlight']['enabled'] && !empty($query)) {
            $searchParams['body']['highlight'] = $this->buildHighlight();
        }

        // Aggregations
        if (!empty($params['aggregations'])) {
            $searchParams['body']['aggs'] = $this->buildAggregations($params['aggregations']);
        }

        // Suggestions
        if (!empty($params['suggest'])) {
            $searchParams['body']['suggest'] = $this->buildSuggestions($params['suggest']);
        }

        return $searchParams;
    }

    /**
     * Ana query oluştur
     */
    protected function buildQuery(string $query, array $filters = []): array
    {
        $boolQuery = [
            'bool' => [
                'must' => [
                    [
                        'multi_match' => [
                            'query' => $query,
                            'fields' => array_keys($this->config['search']['boost']),
                            'type' => 'best_fields',
                            'fuzziness' => 'AUTO',
                            'operator' => 'and',
                            'boost' => 1.0
                        ]
                    ]
                ],
                'should' => [
                    // Exact match boost
                    [
                        'multi_match' => [
                            'query' => $query,
                            'fields' => ['name.keyword^5', 'sku^3'],
                            'type' => 'phrase',
                            'boost' => 2.0
                        ]
                    ],
                    // Autocomplete boost
                    [
                        'match' => [
                            'name.autocomplete' => [
                                'query' => $query,
                                'boost' => 1.5
                            ]
                        ]
                    ]
                ],
                'filter' => $this->buildFilters($filters)
            ]
        ];

        return $boolQuery;
    }

    /**
     * Sadece filtre query'si oluştur
     */
    protected function buildFilterOnlyQuery(array $filters): array
    {
        if (empty($filters)) {
            return ['match_all' => new \stdClass()];
        }

        return [
            'bool' => [
                'filter' => $this->buildFilters($filters)
            ]
        ];
    }

    /**
     * Filtreleri oluştur
     */
    protected function buildFilters(array $filters): array
    {
        $filterQueries = [];

        foreach ($filters as $field => $value) {
            if (empty($value)) {
                continue;
            }

            switch ($field) {
                case 'category_id':
                case 'category_ids':
                    $values = is_array($value) ? $value : [$value];
                    $filterQueries[] = ['terms' => ['category_id' => $values]];
                    break;

                case 'price_min':
                    $filterQueries[] = ['range' => ['price' => ['gte' => $value]]];
                    break;

                case 'price_max':
                    $filterQueries[] = ['range' => ['price' => ['lte' => $value]]];
                    break;

                case 'price_range':
                    if (isset($value['min']) || isset($value['max'])) {
                        $range = [];
                        if (isset($value['min'])) $range['gte'] = $value['min'];
                        if (isset($value['max'])) $range['lte'] = $value['max'];
                        $filterQueries[] = ['range' => ['price' => $range]];
                    }
                    break;

                case 'brand':
                case 'brands':
                    $values = is_array($value) ? $value : [$value];
                    $filterQueries[] = ['terms' => ['brand.keyword' => $values]];
                    break;

                case 'status':
                    $filterQueries[] = ['term' => ['status' => $value]];
                    break;

                case 'in_stock':
                    if ($value) {
                        $filterQueries[] = ['range' => ['stock_quantity' => ['gt' => 0]]];
                    }
                    break;

                case 'attributes':
                    foreach ($value as $attrName => $attrValue) {
                        $filterQueries[] = [
                            'nested' => [
                                'path' => 'attributes',
                                'query' => [
                                    'bool' => [
                                        'must' => [
                                            ['term' => ['attributes.name' => $attrName]],
                                            ['term' => ['attributes.value' => $attrValue]]
                                        ]
                                    ]
                                ]
                            ]
                        ];
                    }
                    break;
            }
        }

        return $filterQueries;
    }

    /**
     * Sıralama oluştur
     */
    protected function buildSort(array $sort): array
    {
        $sortQueries = [];

        foreach ($sort as $field => $direction) {
            switch ($field) {
                case 'relevance':
                    $sortQueries[] = ['_score' => ['order' => 'desc']];
                    break;

                case 'price':
                    $sortQueries[] = ['price' => ['order' => $direction]];
                    break;

                case 'name':
                    $sortQueries[] = ['name.keyword' => ['order' => $direction]];
                    break;

                case 'created_at':
                    $sortQueries[] = ['created_at' => ['order' => $direction]];
                    break;

                case 'popularity':
                    $sortQueries[] = ['popularity_score' => ['order' => $direction]];
                    break;

                case 'sales':
                    $sortQueries[] = ['sales_count' => ['order' => $direction]];
                    break;

                default:
                    $sortQueries[] = [$field => ['order' => $direction]];
                    break;
            }
        }

        return $sortQueries;
    }

    /**
     * Highlighting oluştur
     */
    protected function buildHighlight(): array
    {
        $highlightConfig = $this->config['search']['highlight'];
        
        return [
            'pre_tags' => $highlightConfig['pre_tags'],
            'post_tags' => $highlightConfig['post_tags'],
            'fragment_size' => $highlightConfig['fragment_size'],
            'number_of_fragments' => $highlightConfig['number_of_fragments'],
            'fields' => [
                'name' => new \stdClass(),
                'description' => new \stdClass(),
                'tags' => new \stdClass(),
            ]
        ];
    }

    /**
     * Aggregations oluştur
     */
    protected function buildAggregations(array $aggregations): array
    {
        $aggs = [];

        foreach ($aggregations as $name => $config) {
            switch ($config['type']) {
                case 'terms':
                    $aggs[$name] = [
                        'terms' => [
                            'field' => $config['field'],
                            'size' => $config['size'] ?? 10
                        ]
                    ];
                    break;

                case 'range':
                    $aggs[$name] = [
                        'range' => [
                            'field' => $config['field'],
                            'ranges' => $config['ranges']
                        ]
                    ];
                    break;

                case 'histogram':
                    $aggs[$name] = [
                        'histogram' => [
                            'field' => $config['field'],
                            'interval' => $config['interval']
                        ]
                    ];
                    break;
            }
        }

        return $aggs;
    }

    /**
     * Suggestions oluştur
     */
    protected function buildSuggestions(array $suggestions): array
    {
        $suggests = [];

        foreach ($suggestions as $name => $config) {
            $suggests[$name] = [
                'text' => $config['text'],
                'term' => [
                    'field' => $config['field'],
                    'size' => $config['size'] ?? 5
                ]
            ];
        }

        return $suggests;
    }

    /**
     * Arama sonucunu formatla
     */
    protected function formatSearchResponse(array $response): array
    {
        $hits = [];
        $total = $response['hits']['total']['value'] ?? 0;

        foreach ($response['hits']['hits'] ?? [] as $hit) {
            $source = $hit['_source'];
            $source['_score'] = $hit['_score'];
            
            if (isset($hit['highlight'])) {
                $source['_highlight'] = $hit['highlight'];
            }
            
            $hits[] = $source;
        }

        return [
            'hits' => $hits,
            'total' => $total,
            'max_score' => $response['hits']['max_score'] ?? null,
            'aggregations' => $response['aggregations'] ?? [],
            'suggestions' => $response['suggest'] ?? [],
            'took' => $response['took'] ?? 0,
        ];
    }

    /**
     * Belge ekle veya güncelle
     */
    public function indexDocument(string $index, string $id, array $document): bool
    {
        try {
            $this->client->index([
                'index' => $index,
                'id' => $id,
                'body' => $document
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error("Elasticsearch document indexing failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Toplu belge ekleme
     */
    public function bulkIndex(string $index, array $documents): bool
    {
        try {
            $params = ['body' => []];

            foreach ($documents as $id => $document) {
                $params['body'][] = [
                    'index' => [
                        '_index' => $index,
                        '_id' => $id
                    ]
                ];
                $params['body'][] = $document;
            }

            $response = $this->client->bulk($params);
            
            if ($response['errors']) {
                Log::warning('Elasticsearch bulk indexing had errors', $response['items']);
            }

            return !$response['errors'];
        } catch (\Exception $e) {
            Log::error("Elasticsearch bulk indexing failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Belge sil
     */
    public function deleteDocument(string $index, string $id): bool
    {
        try {
            $this->client->delete([
                'index' => $index,
                'id' => $id
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error("Elasticsearch document deletion failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Index sil
     */
    public function deleteIndex(string $index): bool
    {
        try {
            $this->client->indices()->delete(['index' => $index]);
            return true;
        } catch (\Exception $e) {
            Log::error("Elasticsearch index deletion failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * Client'ı al
     */
    public function getClient(): Client
    {
        return $this->client;
    }

    /**
     * Index istatistiklerini al
     */
    public function getIndexStats(string $index): array
    {
        try {
            return $this->client->indices()->stats(['index' => $index]);
        } catch (\Exception $e) {
            Log::error("Elasticsearch index stats failed: {$e->getMessage()}");
            return [];
        }
    }

    /**
     * Cluster sağlığını kontrol et
     */
    public function getClusterHealth(): array
    {
        try {
            return $this->client->cluster()->health();
        } catch (\Exception $e) {
            Log::error("Elasticsearch cluster health check failed: {$e->getMessage()}");
            return ['status' => 'red', 'error' => $e->getMessage()];
        }
    }
}
