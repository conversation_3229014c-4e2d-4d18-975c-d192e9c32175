<?php

namespace App\Core\Infrastructure\Queue\Contracts;

/**
 * Prioritizable Job Interface
 * Job önceliklendirme için interface
 */
interface PrioritizableJobInterface
{
    /**
     * Job'ın öncelik seviyesini al
     * 1 = En yüksek öncelik, 10 = En düşük öncelik
     */
    public function getPriority(): int;

    /**
     * Job'ın SLA (Service Level Agreement) süresini al (saniye)
     */
    public function getSLA(): int;

    /**
     * Job'ın kategori/türünü al
     */
    public function getJobCategory(): string;

    /**
     * Job'ın business impact seviyesini al
     */
    public function getBusinessImpact(): string;

    /**
     * Job'ın resource requirements'ını al
     */
    public function getResourceRequirements(): array;

    /**
     * Job'ın dependency'lerini al
     */
    public function getDependencies(): array;

    /**
     * Job'ın deadline'ını al (opsiyonel)
     */
    public function getDeadline(): ?\DateTimeInterface;

    /**
     * Job'ın retry strategy'sini al
     */
    public function getRetryStrategy(): array;

    /**
     * Job'ın escalation rules'ını al
     */
    public function getEscalationRules(): array;

    /**
     * Dynamic priority hesaplama
     * Context'e göre öncelik değişebilir
     */
    public function calculateDynamicPriority(array $context = []): int;

    /**
     * Job'ın queue routing bilgisini al
     */
    public function getQueueRouting(): array;
}
