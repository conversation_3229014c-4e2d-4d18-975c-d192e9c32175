<?php

namespace App\Core\Infrastructure\Queue\Contracts;

/**
 * Batch Processable Interface
 * Batch processing için interface
 */
interface BatchProcessableInterface
{
    /**
     * Batch boyutunu al
     */
    public function getBatchSize(): int;

    /**
     * Batch timeout süresini al (saniye)
     */
    public function getBatchTimeout(): int;

    /**
     * Batch'in paralel işlenip işlenemeyeceğini belirle
     */
    public function canProcessInParallel(): bool;

    /**
     * Batch'in chunk'lara bölünüp bölünemeyeceğini belirle
     */
    public function canChunk(): bool;

    /**
     * Chunk boyutunu al
     */
    public function getChunkSize(): int;

    /**
     * Batch progress tracking gerekli mi?
     */
    public function requiresProgressTracking(): bool;

    /**
     * Batch failure handling strategy'sini al
     */
    public function getFailureHandlingStrategy(): string;

    /**
     * Batch completion callback'ini al
     */
    public function getBatchCompletionCallback(): ?callable;

    /**
     * Batch progress callback'ini al
     */
    public function getBatchProgressCallback(): ?callable;

    /**
     * Batch'in retry edilebilir olup olmadığını belirle
     */
    public function isRetryable(): bool;

    /**
     * Batch retry strategy'sini al
     */
    public function getBatchRetryStrategy(): array;

    /**
     * Batch dependencies'ini al
     */
    public function getBatchDependencies(): array;

    /**
     * Batch resource requirements'ını al
     */
    public function getBatchResourceRequirements(): array;
}
