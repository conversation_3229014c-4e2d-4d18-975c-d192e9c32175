<?php

namespace App\Core\Infrastructure\Queue\Contracts;

/**
 * QueueAnalyticsInterface
 * Queue analytics contract
 */
interface QueueAnalyticsInterface
{
    /**
     * Queue metriklerini al
     */
    public function getMetrics(string $period = 'hour'): array;

    /**
     * Job istatistiklerini al
     */
    public function getJobStatistics(): array;

    /**
     * Failed job'ları al
     */
    public function getFailedJobs(int $limit = 50): array;

    /**
     * Queue durumunu al
     */
    public function getQueueStatus(): array;

    /**
     * Performance metriklerini al
     */
    public function getPerformanceMetrics(): array;

    /**
     * Real-time metrikleri al
     */
    public function getRealTimeMetrics(): array;

    /**
     * Queue sağlık kontrolü
     */
    public function healthCheck(): array;

    /**
     * Load balancing önerilerini al
     */
    public function getLoadBalancingRecommendations(): array;
}
