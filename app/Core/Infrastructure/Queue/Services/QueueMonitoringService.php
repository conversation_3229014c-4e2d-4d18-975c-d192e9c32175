<?php

namespace App\Core\Infrastructure\Queue\Services;

use App\Core\Infrastructure\Queue\Contracts\QueueMonitoringInterface;
use App\Core\Infrastructure\Queue\Contracts\QueueAnalyticsInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Notification;
use Carbon\Carbon;

/**
 * Queue Monitoring Service
 * Queue izleme ve uyarı sistemi
 */
class QueueMonitoringService implements QueueMonitoringInterface
{
    protected QueueAnalyticsInterface $analytics;
    protected array $config;
    protected array $alertThresholds;

    public function __construct(QueueAnalyticsInterface $analytics, array $config = [])
    {
        $this->analytics = $analytics;
        $this->config = array_merge([
            'monitoring_enabled' => true,
            'alert_enabled' => true,
            'health_check_interval' => 60, // seconds
            'alert_cooldown' => 300, // 5 minutes
            'notification_channels' => ['database', 'log'],
        ], $config);

        $this->alertThresholds = [
            'high_failure_rate' => 0.1, // 10%
            'queue_backlog' => 1000, // jobs
            'long_processing_time' => 300, // 5 minutes
            'memory_usage_high' => 512 * 1024 * 1024, // 512MB
            'worker_down_threshold' => 0, // minimum workers
            'response_time_high' => 5000, // 5 seconds
        ];
    }

    /**
     * Queue sağlık kontrolü gerçekleştir
     */
    public function performHealthCheck(): array
    {
        if (!$this->config['monitoring_enabled']) {
            return ['status' => 'monitoring_disabled'];
        }

        $healthStatus = [
            'timestamp' => now(),
            'overall_status' => 'healthy',
            'checks' => [],
            'alerts' => [],
            'recommendations' => [],
        ];

        // Temel sağlık kontrolleri
        $checks = [
            'queue_backlog' => $this->checkQueueBacklog(),
            'failure_rate' => $this->checkFailureRate(),
            'worker_status' => $this->checkWorkerStatus(),
            'processing_time' => $this->checkProcessingTime(),
            'memory_usage' => $this->checkMemoryUsage(),
            'response_time' => $this->checkResponseTime(),
        ];

        foreach ($checks as $checkName => $result) {
            $healthStatus['checks'][$checkName] = $result;

            if ($result['status'] === 'critical') {
                $healthStatus['overall_status'] = 'critical';
                $this->triggerAlert($checkName, $result);
            } elseif ($result['status'] === 'warning' && $healthStatus['overall_status'] === 'healthy') {
                $healthStatus['overall_status'] = 'warning';
            }

            if (!empty($result['recommendations'])) {
                $healthStatus['recommendations'] = array_merge(
                    $healthStatus['recommendations'],
                    $result['recommendations']
                );
            }
        }

        // Sağlık durumunu kaydet
        $this->recordHealthSnapshot($healthStatus);

        return $healthStatus;
    }

    /**
     * Queue backlog kontrolü
     */
    protected function checkQueueBacklog(): array
    {
        $stats = $this->analytics->getQueueStatistics();
        $pendingJobs = $stats['pending_jobs'];
        $threshold = $this->alertThresholds['queue_backlog'];

        $result = [
            'status' => 'healthy',
            'value' => $pendingJobs,
            'threshold' => $threshold,
            'message' => "Pending jobs: {$pendingJobs}",
            'recommendations' => [],
        ];

        if ($pendingJobs > $threshold * 2) {
            $result['status'] = 'critical';
            $result['message'] = "Critical queue backlog: {$pendingJobs} pending jobs";
            $result['recommendations'][] = 'Scale up workers immediately';
            $result['recommendations'][] = 'Check for stuck jobs';
        } elseif ($pendingJobs > $threshold) {
            $result['status'] = 'warning';
            $result['message'] = "High queue backlog: {$pendingJobs} pending jobs";
            $result['recommendations'][] = 'Consider scaling up workers';
        }

        return $result;
    }

    /**
     * Failure rate kontrolü
     */
    protected function checkFailureRate(): array
    {
        $stats = $this->analytics->getQueueStatistics();
        $failureRate = 1 - $stats['success_rate'];
        $threshold = $this->alertThresholds['high_failure_rate'];

        $result = [
            'status' => 'healthy',
            'value' => $failureRate,
            'threshold' => $threshold,
            'message' => sprintf("Failure rate: %.2f%%", $failureRate * 100),
            'recommendations' => [],
        ];

        if ($failureRate > $threshold * 2) {
            $result['status'] = 'critical';
            $result['message'] = sprintf("Critical failure rate: %.2f%%", $failureRate * 100);
            $result['recommendations'][] = 'Investigate job failures immediately';
            $result['recommendations'][] = 'Check error logs';
        } elseif ($failureRate > $threshold) {
            $result['status'] = 'warning';
            $result['message'] = sprintf("High failure rate: %.2f%%", $failureRate * 100);
            $result['recommendations'][] = 'Monitor job failures';
        }

        return $result;
    }

    /**
     * Worker durumu kontrolü
     */
    protected function checkWorkerStatus(): array
    {
        $activeWorkers = $this->getActiveWorkerCount();
        $threshold = $this->alertThresholds['worker_down_threshold'];

        $result = [
            'status' => 'healthy',
            'value' => $activeWorkers,
            'threshold' => $threshold,
            'message' => "Active workers: {$activeWorkers}",
            'recommendations' => [],
        ];

        if ($activeWorkers <= $threshold) {
            $result['status'] = 'critical';
            $result['message'] = "No active workers detected";
            $result['recommendations'][] = 'Start queue workers immediately';
            $result['recommendations'][] = 'Check Horizon/Supervisor status';
        } elseif ($activeWorkers < 3) {
            $result['status'] = 'warning';
            $result['message'] = "Low number of active workers: {$activeWorkers}";
            $result['recommendations'][] = 'Consider adding more workers';
        }

        return $result;
    }

    /**
     * İşlem süresi kontrolü
     */
    protected function checkProcessingTime(): array
    {
        $metrics = $this->analytics->getJobPerformanceMetrics(null, ['period' => 'last_hour']);
        $avgTime = $metrics['avg_execution_time'];
        $threshold = $this->alertThresholds['long_processing_time'];

        $result = [
            'status' => 'healthy',
            'value' => $avgTime,
            'threshold' => $threshold,
            'message' => sprintf("Average processing time: %.2fs", $avgTime),
            'recommendations' => [],
        ];

        if ($avgTime > $threshold * 2) {
            $result['status'] = 'critical';
            $result['message'] = sprintf("Very long processing time: %.2fs", $avgTime);
            $result['recommendations'][] = 'Investigate slow jobs';
            $result['recommendations'][] = 'Check for performance bottlenecks';
        } elseif ($avgTime > $threshold) {
            $result['status'] = 'warning';
            $result['message'] = sprintf("Long processing time: %.2fs", $avgTime);
            $result['recommendations'][] = 'Monitor job performance';
        }

        return $result;
    }

    /**
     * Memory kullanımı kontrolü
     */
    protected function checkMemoryUsage(): array
    {
        $metrics = $this->analytics->getJobPerformanceMetrics(null, ['period' => 'last_hour']);
        $avgMemory = $metrics['avg_memory_usage'];
        $threshold = $this->alertThresholds['memory_usage_high'];

        $result = [
            'status' => 'healthy',
            'value' => $avgMemory,
            'threshold' => $threshold,
            'message' => sprintf("Average memory usage: %.2fMB", $avgMemory / 1024 / 1024),
            'recommendations' => [],
        ];

        if ($avgMemory > $threshold * 1.5) {
            $result['status'] = 'critical';
            $result['message'] = sprintf("Very high memory usage: %.2fMB", $avgMemory / 1024 / 1024);
            $result['recommendations'][] = 'Check for memory leaks';
            $result['recommendations'][] = 'Restart workers if necessary';
        } elseif ($avgMemory > $threshold) {
            $result['status'] = 'warning';
            $result['message'] = sprintf("High memory usage: %.2fMB", $avgMemory / 1024 / 1024);
            $result['recommendations'][] = 'Monitor memory usage';
        }

        return $result;
    }

    /**
     * Response time kontrolü
     */
    protected function checkResponseTime(): array
    {
        $throughput = $this->analytics->getThroughputAnalysis(null, 'last_hour');
        $avgResponseTime = $throughput['summary']['avg_jobs_per_hour'] > 0 
            ? 3600 / $throughput['summary']['avg_jobs_per_hour'] * 1000 // ms
            : 0;
        
        $threshold = $this->alertThresholds['response_time_high'];

        $result = [
            'status' => 'healthy',
            'value' => $avgResponseTime,
            'threshold' => $threshold,
            'message' => sprintf("Average response time: %.0fms", $avgResponseTime),
            'recommendations' => [],
        ];

        if ($avgResponseTime > $threshold * 2) {
            $result['status'] = 'critical';
            $result['message'] = sprintf("Very slow response time: %.0fms", $avgResponseTime);
            $result['recommendations'][] = 'Investigate performance issues';
        } elseif ($avgResponseTime > $threshold) {
            $result['status'] = 'warning';
            $result['message'] = sprintf("Slow response time: %.0fms", $avgResponseTime);
            $result['recommendations'][] = 'Monitor performance';
        }

        return $result;
    }

    /**
     * Alert tetikle
     */
    protected function triggerAlert(string $alertType, array $checkResult): void
    {
        if (!$this->config['alert_enabled']) {
            return;
        }

        // Cooldown kontrolü
        $cooldownKey = "alert_cooldown:{$alertType}";
        if (Cache::has($cooldownKey)) {
            return;
        }

        // Alert kaydı oluştur
        $alertId = DB::table('queue_alerts')->insertGetId([
            'alert_type' => $alertType,
            'severity' => $checkResult['status'],
            'title' => $this->getAlertTitle($alertType, $checkResult),
            'description' => $checkResult['message'],
            'alert_data' => json_encode($checkResult),
            'trigger_conditions' => json_encode([
                'threshold' => $checkResult['threshold'],
                'actual_value' => $checkResult['value'],
            ]),
            'threshold_value' => $checkResult['threshold'],
            'actual_value' => $checkResult['value'],
            'triggered_at' => now(),
            'notification_channels' => json_encode($this->config['notification_channels']),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Notification gönder
        $this->sendAlertNotification($alertId, $alertType, $checkResult);

        // Cooldown ayarla
        Cache::put($cooldownKey, true, $this->config['alert_cooldown']);

        Log::warning("Queue alert triggered: {$alertType}", [
            'alert_id' => $alertId,
            'severity' => $checkResult['status'],
            'value' => $checkResult['value'],
            'threshold' => $checkResult['threshold'],
        ]);
    }

    /**
     * Alert başlığı oluştur
     */
    protected function getAlertTitle(string $alertType, array $checkResult): string
    {
        $titles = [
            'queue_backlog' => 'Queue Backlog Alert',
            'high_failure_rate' => 'High Failure Rate Alert',
            'worker_status' => 'Worker Status Alert',
            'long_processing_time' => 'Long Processing Time Alert',
            'memory_usage_high' => 'High Memory Usage Alert',
            'response_time_high' => 'Slow Response Time Alert',
        ];

        $title = $titles[$alertType] ?? 'Queue Alert';
        return "{$title} - {$checkResult['status']}";
    }

    /**
     * Alert notification gönder
     */
    protected function sendAlertNotification(int $alertId, string $alertType, array $checkResult): void
    {
        foreach ($this->config['notification_channels'] as $channel) {
            try {
                switch ($channel) {
                    case 'log':
                        Log::alert("Queue Alert: {$alertType}", $checkResult);
                        break;
                    
                    case 'database':
                        // Already stored in database
                        break;
                    
                    case 'email':
                        // Email notification implementation
                        $this->sendEmailAlert($alertId, $alertType, $checkResult);
                        break;
                    
                    case 'slack':
                        // Slack notification implementation
                        $this->sendSlackAlert($alertId, $alertType, $checkResult);
                        break;
                }
            } catch (\Exception $e) {
                Log::error("Failed to send alert notification via {$channel}", [
                    'alert_id' => $alertId,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Notification sent flag'i güncelle
        DB::table('queue_alerts')
            ->where('id', $alertId)
            ->update(['notifications_sent' => true]);
    }

    /**
     * Email alert gönder
     */
    protected function sendEmailAlert(int $alertId, string $alertType, array $checkResult): void
    {
        // Email notification implementation
        // Bu kısım projenin email sistemi ile entegre edilebilir
    }

    /**
     * Slack alert gönder
     */
    protected function sendSlackAlert(int $alertId, string $alertType, array $checkResult): void
    {
        // Slack notification implementation
        // Bu kısım Slack webhook ile entegre edilebilir
    }

    /**
     * Sağlık durumu snapshot'ı kaydet
     */
    protected function recordHealthSnapshot(array $healthStatus): void
    {
        DB::table('queue_health_snapshots')->insert([
            'snapshot_time' => $healthStatus['timestamp'],
            'overall_status' => $healthStatus['overall_status'],
            'queue_status' => json_encode($healthStatus['checks']),
            'total_pending_jobs' => $healthStatus['checks']['queue_backlog']['value'] ?? 0,
            'active_workers' => $healthStatus['checks']['worker_status']['value'] ?? 0,
            'avg_memory_usage_mb' => isset($healthStatus['checks']['memory_usage']['value']) 
                ? $healthStatus['checks']['memory_usage']['value'] / 1024 / 1024 
                : 0,
            'active_alerts' => json_encode($this->getActiveAlerts()),
            'recommendations' => json_encode($healthStatus['recommendations']),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Aktif alert'leri al
     */
    protected function getActiveAlerts(): array
    {
        return DB::table('queue_alerts')
            ->where('status', 'active')
            ->where('triggered_at', '>', Carbon::now()->subHour())
            ->select('id', 'alert_type', 'severity', 'triggered_at')
            ->get()
            ->toArray();
    }

    /**
     * Aktif worker sayısını al
     */
    protected function getActiveWorkerCount(): int
    {
        // Bu method QueueAnalyticsService'teki ile aynı
        // Redis'ten worker bilgilerini al
        try {
            return DB::table('queue_worker_stats')
                ->where('status', 'active')
                ->where('last_activity', '>', Carbon::now()->subMinutes(5))
                ->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Alert'i çöz
     */
    public function resolveAlert(int $alertId, string $reason = null): bool
    {
        $updated = DB::table('queue_alerts')
            ->where('id', $alertId)
            ->where('status', '!=', 'resolved')
            ->update([
                'status' => 'resolved',
                'resolved_at' => now(),
                'updated_at' => now(),
            ]);

        if ($updated) {
            Log::info("Queue alert resolved", [
                'alert_id' => $alertId,
                'reason' => $reason,
            ]);
        }

        return $updated > 0;
    }

    /**
     * Alert'i onayla
     */
    public function acknowledgeAlert(int $alertId, string $acknowledgedBy = null): bool
    {
        $updated = DB::table('queue_alerts')
            ->where('id', $alertId)
            ->where('status', 'active')
            ->update([
                'status' => 'acknowledged',
                'acknowledged_at' => now(),
                'assigned_to' => $acknowledgedBy,
                'updated_at' => now(),
            ]);

        if ($updated) {
            Log::info("Queue alert acknowledged", [
                'alert_id' => $alertId,
                'acknowledged_by' => $acknowledgedBy,
            ]);
        }

        return $updated > 0;
    }

    /**
     * Alert geçmişini al
     */
    public function getAlertHistory(array $filters = []): array
    {
        $query = DB::table('queue_alerts')
            ->orderByDesc('triggered_at');

        if (isset($filters['alert_type'])) {
            $query->where('alert_type', $filters['alert_type']);
        }

        if (isset($filters['severity'])) {
            $query->where('severity', $filters['severity']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['from_date'])) {
            $query->where('triggered_at', '>=', $filters['from_date']);
        }

        if (isset($filters['to_date'])) {
            $query->where('triggered_at', '<=', $filters['to_date']);
        }

        $limit = $filters['limit'] ?? 100;
        
        return $query->limit($limit)->get()->toArray();
    }
}
