<?php

namespace App\Core\Infrastructure\Queue\Services;

use App\Core\Infrastructure\Queue\Contracts\RetryStrategyInterface;
use App\Core\Infrastructure\Queue\Contracts\QueueAnalyticsInterface;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * Advanced Job Retry Strategy
 * Gelişmiş job yeniden deneme stratejileri
 */
class AdvancedRetryStrategy implements RetryStrategyInterface
{
    protected QueueAnalyticsInterface $analytics;
    protected array $config;

    public function __construct(QueueAnalyticsInterface $analytics, array $config = [])
    {
        $this->analytics = $analytics;
        $this->config = array_merge([
            'max_attempts' => 5,
            'base_delay' => 60, // seconds
            'max_delay' => 3600, // 1 hour
            'backoff_strategy' => 'exponential', // linear, exponential, fibonacci
            'jitter_enabled' => true,
            'circuit_breaker_enabled' => true,
            'adaptive_retry_enabled' => true,
            'failure_rate_threshold' => 0.5, // 50%
            'min_requests_for_circuit_breaker' => 10,
        ], $config);
    }

    /**
     * Job için retry stratejisini belirle
     */
    public function determineRetryStrategy($job, \Throwable $exception, int $attempts): array
    {
        $jobClass = get_class($job);
        $exceptionType = get_class($exception);

        // Circuit breaker kontrolü
        if ($this->config['circuit_breaker_enabled'] && $this->isCircuitBreakerOpen($jobClass)) {
            return [
                'should_retry' => false,
                'reason' => 'circuit_breaker_open',
                'delay' => 0,
                'max_attempts' => 0,
            ];
        }

        // Exception type'a göre retry stratejisi
        $strategy = $this->getStrategyForException($exceptionType);
        
        // Adaptive retry kontrolü
        if ($this->config['adaptive_retry_enabled']) {
            $strategy = $this->adaptStrategyBasedOnHistory($jobClass, $strategy);
        }

        // Retry delay hesaplama
        $delay = $this->calculateRetryDelay($attempts, $strategy);

        // Max attempts kontrolü
        $maxAttempts = $strategy['max_attempts'] ?? $this->config['max_attempts'];
        $shouldRetry = $attempts < $maxAttempts && $this->shouldRetryForException($exception);

        return [
            'should_retry' => $shouldRetry,
            'delay' => $delay,
            'max_attempts' => $maxAttempts,
            'strategy' => $strategy['name'],
            'reason' => $shouldRetry ? 'retry_allowed' : 'max_attempts_reached',
        ];
    }

    /**
     * Exception type'a göre strateji belirle
     */
    protected function getStrategyForException(string $exceptionType): array
    {
        $strategies = [
            // Network/Connection errors - aggressive retry
            'Illuminate\Http\Client\ConnectionException' => [
                'name' => 'network_aggressive',
                'max_attempts' => 8,
                'backoff_strategy' => 'exponential',
                'base_delay' => 30,
                'max_delay' => 1800, // 30 minutes
            ],
            
            // Database errors - moderate retry
            'Illuminate\Database\QueryException' => [
                'name' => 'database_moderate',
                'max_attempts' => 5,
                'backoff_strategy' => 'linear',
                'base_delay' => 60,
                'max_delay' => 600, // 10 minutes
            ],
            
            // Rate limiting - patient retry
            'Illuminate\Http\Client\RequestException' => [
                'name' => 'rate_limit_patient',
                'max_attempts' => 10,
                'backoff_strategy' => 'fibonacci',
                'base_delay' => 120,
                'max_delay' => 3600, // 1 hour
            ],
            
            // Memory errors - no retry
            'Symfony\Component\Process\Exception\ProcessTimedOutException' => [
                'name' => 'timeout_no_retry',
                'max_attempts' => 1,
                'backoff_strategy' => 'none',
                'base_delay' => 0,
                'max_delay' => 0,
            ],
            
            // Default strategy
            'default' => [
                'name' => 'default_moderate',
                'max_attempts' => $this->config['max_attempts'],
                'backoff_strategy' => $this->config['backoff_strategy'],
                'base_delay' => $this->config['base_delay'],
                'max_delay' => $this->config['max_delay'],
            ],
        ];

        return $strategies[$exceptionType] ?? $strategies['default'];
    }

    /**
     * Retry delay hesapla
     */
    protected function calculateRetryDelay(int $attempts, array $strategy): int
    {
        $baseDelay = $strategy['base_delay'];
        $maxDelay = $strategy['max_delay'];
        $backoffStrategy = $strategy['backoff_strategy'];

        $delay = match ($backoffStrategy) {
            'linear' => $baseDelay * $attempts,
            'exponential' => $baseDelay * pow(2, $attempts - 1),
            'fibonacci' => $this->calculateFibonacciDelay($baseDelay, $attempts),
            'none' => 0,
            default => $baseDelay * $attempts,
        };

        // Max delay sınırı
        $delay = min($delay, $maxDelay);

        // Jitter ekle (rastgele gecikme)
        if ($this->config['jitter_enabled'] && $delay > 0) {
            $jitter = rand(0, (int)($delay * 0.1)); // %10 jitter
            $delay += $jitter;
        }

        return $delay;
    }

    /**
     * Fibonacci delay hesapla
     */
    protected function calculateFibonacciDelay(int $baseDelay, int $attempts): int
    {
        $fibonacci = [1, 1];
        
        for ($i = 2; $i < $attempts; $i++) {
            $fibonacci[$i] = $fibonacci[$i - 1] + $fibonacci[$i - 2];
        }

        $multiplier = $fibonacci[$attempts - 1] ?? 1;
        return $baseDelay * $multiplier;
    }

    /**
     * Exception için retry yapılıp yapılmayacağını belirle
     */
    protected function shouldRetryForException(\Throwable $exception): bool
    {
        $nonRetryableExceptions = [
            'Symfony\Component\HttpKernel\Exception\NotFoundHttpException',
            'Illuminate\Auth\AuthenticationException',
            'Illuminate\Validation\ValidationException',
            'InvalidArgumentException',
            'TypeError',
            'ParseError',
        ];

        foreach ($nonRetryableExceptions as $exceptionClass) {
            if ($exception instanceof $exceptionClass) {
                return false;
            }
        }

        return true;
    }

    /**
     * Circuit breaker açık mı kontrol et
     */
    protected function isCircuitBreakerOpen(string $jobClass): bool
    {
        $cacheKey = "circuit_breaker:{$jobClass}";
        $circuitState = Cache::get($cacheKey);

        if (!$circuitState) {
            return false;
        }

        // Circuit breaker açık ve timeout süresi geçmemişse
        if ($circuitState['state'] === 'open' && 
            Carbon::parse($circuitState['opened_at'])->addMinutes(5)->isFuture()) {
            return true;
        }

        // Half-open state - test için bir job'a izin ver
        if ($circuitState['state'] === 'half_open') {
            return false;
        }

        return false;
    }

    /**
     * Geçmiş verilere göre stratejiyi adapte et
     */
    protected function adaptStrategyBasedOnHistory(string $jobClass, array $strategy): array
    {
        $analytics = $this->analytics->getJobAnalytics($jobClass, [
            'period' => 'last_24_hours',
            'metrics' => ['success_rate', 'avg_retry_count', 'failure_patterns'],
        ]);

        $successRate = $analytics['success_rate'] ?? 1.0;
        $avgRetryCount = $analytics['avg_retry_count'] ?? 1;

        // Düşük başarı oranı varsa daha agresif retry
        if ($successRate < 0.7) {
            $strategy['max_attempts'] = min($strategy['max_attempts'] + 2, 10);
            $strategy['base_delay'] = (int)($strategy['base_delay'] * 1.5);
        }

        // Yüksek retry count varsa daha konservatif yaklaşım
        if ($avgRetryCount > 3) {
            $strategy['base_delay'] = (int)($strategy['base_delay'] * 2);
            $strategy['max_delay'] = min($strategy['max_delay'] * 2, 7200); // Max 2 hours
        }

        return $strategy;
    }

    /**
     * Job başarısızlığını kaydet (circuit breaker için)
     */
    public function recordJobFailure(string $jobClass, \Throwable $exception): void
    {
        if (!$this->config['circuit_breaker_enabled']) {
            return;
        }

        $cacheKey = "circuit_breaker_stats:{$jobClass}";
        $stats = Cache::get($cacheKey, [
            'total_requests' => 0,
            'failed_requests' => 0,
            'last_failure' => null,
        ]);

        $stats['total_requests']++;
        $stats['failed_requests']++;
        $stats['last_failure'] = now();

        Cache::put($cacheKey, $stats, 3600); // 1 hour

        // Circuit breaker açma kontrolü
        $this->checkAndOpenCircuitBreaker($jobClass, $stats);
    }

    /**
     * Job başarısını kaydet
     */
    public function recordJobSuccess(string $jobClass): void
    {
        if (!$this->config['circuit_breaker_enabled']) {
            return;
        }

        $cacheKey = "circuit_breaker_stats:{$jobClass}";
        $stats = Cache::get($cacheKey, [
            'total_requests' => 0,
            'failed_requests' => 0,
            'last_failure' => null,
        ]);

        $stats['total_requests']++;

        Cache::put($cacheKey, $stats, 3600);

        // Circuit breaker kapatma kontrolü
        $this->checkAndCloseCircuitBreaker($jobClass, $stats);
    }

    /**
     * Circuit breaker açma kontrolü
     */
    protected function checkAndOpenCircuitBreaker(string $jobClass, array $stats): void
    {
        if ($stats['total_requests'] < $this->config['min_requests_for_circuit_breaker']) {
            return;
        }

        $failureRate = $stats['failed_requests'] / $stats['total_requests'];

        if ($failureRate >= $this->config['failure_rate_threshold']) {
            $cacheKey = "circuit_breaker:{$jobClass}";
            Cache::put($cacheKey, [
                'state' => 'open',
                'opened_at' => now(),
                'failure_rate' => $failureRate,
            ], 3600);

            Log::warning("Circuit breaker opened for job class: {$jobClass}", [
                'failure_rate' => $failureRate,
                'total_requests' => $stats['total_requests'],
                'failed_requests' => $stats['failed_requests'],
            ]);
        }
    }

    /**
     * Circuit breaker kapatma kontrolü
     */
    protected function checkAndCloseCircuitBreaker(string $jobClass, array $stats): void
    {
        $cacheKey = "circuit_breaker:{$jobClass}";
        $circuitState = Cache::get($cacheKey);

        if ($circuitState && $circuitState['state'] === 'half_open') {
            // Half-open state'de başarılı job varsa circuit'i kapat
            Cache::forget($cacheKey);
            
            Log::info("Circuit breaker closed for job class: {$jobClass}");
        }
    }

    /**
     * Retry stratejisi istatistiklerini al
     */
    public function getRetryStatistics(string $jobClass = null): array
    {
        $stats = [
            'total_retries' => 0,
            'successful_retries' => 0,
            'failed_retries' => 0,
            'avg_retry_delay' => 0,
            'circuit_breaker_activations' => 0,
        ];

        // Analytics'ten veri al
        if ($jobClass) {
            $analytics = $this->analytics->getJobAnalytics($jobClass, [
                'period' => 'last_7_days',
                'metrics' => ['retry_stats'],
            ]);
            
            $stats = array_merge($stats, $analytics['retry_stats'] ?? []);
        }

        return $stats;
    }
}
