<?php

namespace App\Core\Infrastructure\Queue\Services;

use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

/**
 * Queue Health Monitor
 * Queue sağlık durumu izleme ve monitoring
 */
class QueueHealthMonitor
{
    protected array $config;
    protected array $thresholds;
    protected array $alertChannels;

    public function __construct()
    {
        $this->config = config('queue_infrastructure.health_monitoring', []);
        $this->thresholds = $this->config['thresholds'] ?? [];
        $this->alertChannels = $this->config['alert_channels'] ?? [];
    }

    /**
     * Genel queue sağlık durumunu kontrol et
     */
    public function checkOverallHealth(): array
    {
        $checks = [
            'queue_connectivity' => $this->checkQueueConnectivity(),
            'queue_sizes' => $this->checkQueueSizes(),
            'worker_status' => $this->checkWorkerStatus(),
            'failed_jobs' => $this->checkFailedJobs(),
            'job_processing_rate' => $this->checkJobProcessingRate(),
            'memory_usage' => $this->checkMemoryUsage(),
            'response_times' => $this->checkResponseTimes(),
            'dead_letter_queue' => $this->checkDeadLetterQueue(),
        ];

        $overallStatus = $this->calculateOverallStatus($checks);

        $healthReport = [
            'overall_status' => $overallStatus,
            'timestamp' => now()->toISOString(),
            'checks' => $checks,
            'summary' => $this->generateHealthSummary($checks),
            'recommendations' => $this->generateRecommendations($checks),
        ];

        // Kritik durumda alert gönder
        if ($overallStatus === 'critical') {
            $this->sendCriticalAlert($healthReport);
        }

        return $healthReport;
    }

    /**
     * Queue connectivity kontrolü
     */
    public function checkQueueConnectivity(): array
    {
        $results = [];
        $connections = config('queue.connections');

        foreach ($connections as $name => $config) {
            if ($config['driver'] === 'sync') {
                continue; // Sync driver'ı skip et
            }

            try {
                $startTime = microtime(true);
                
                switch ($config['driver']) {
                    case 'redis':
                        $redis = Redis::connection($config['connection'] ?? 'default');
                        $redis->ping();
                        break;
                    case 'database':
                        DB::connection($config['connection'] ?? null)->getPdo();
                        break;
                    default:
                        // Diğer driver'lar için genel test
                        break;
                }

                $responseTime = (microtime(true) - $startTime) * 1000;
                
                $results[$name] = [
                    'status' => 'healthy',
                    'response_time' => round($responseTime, 2),
                    'driver' => $config['driver'],
                ];

            } catch (\Exception $e) {
                $results[$name] = [
                    'status' => 'error',
                    'error' => $e->getMessage(),
                    'driver' => $config['driver'],
                ];
            }
        }

        return [
            'status' => $this->determineConnectivityStatus($results),
            'connections' => $results,
            'healthy_connections' => count(array_filter($results, fn($r) => $r['status'] === 'healthy')),
            'total_connections' => count($results),
        ];
    }

    /**
     * Queue boyutlarını kontrol et
     */
    public function checkQueueSizes(): array
    {
        $queueSizes = [];
        $alerts = [];
        
        try {
            $redis = Redis::connection();
            $queues = $this->config['monitored_queues'] ?? ['default', 'high', 'low', 'emails', 'orders'];

            foreach ($queues as $queueName) {
                $size = $redis->llen("queues:{$queueName}");
                $threshold = $this->thresholds['queue_size'][$queueName] ?? $this->thresholds['queue_size']['default'] ?? 1000;
                
                $status = 'healthy';
                if ($size > $threshold * 0.9) {
                    $status = 'critical';
                    $alerts[] = "Queue {$queueName} is near capacity ({$size}/{$threshold})";
                } elseif ($size > $threshold * 0.7) {
                    $status = 'warning';
                    $alerts[] = "Queue {$queueName} is getting full ({$size}/{$threshold})";
                }

                $queueSizes[$queueName] = [
                    'size' => $size,
                    'threshold' => $threshold,
                    'utilization' => round(($size / $threshold) * 100, 2),
                    'status' => $status,
                ];
            }

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }

        return [
            'status' => $this->determineQueueSizeStatus($queueSizes),
            'queues' => $queueSizes,
            'alerts' => $alerts,
            'total_pending_jobs' => array_sum(array_column($queueSizes, 'size')),
        ];
    }

    /**
     * Worker durumunu kontrol et
     */
    public function checkWorkerStatus(): array
    {
        try {
            // Horizon worker'larını kontrol et
            $horizonStatus = $this->checkHorizonStatus();
            
            // Aktif worker'ları kontrol et
            $activeWorkers = $this->getActiveWorkers();
            
            // Worker performance'ını kontrol et
            $workerPerformance = $this->checkWorkerPerformance();

            $status = 'healthy';
            $alerts = [];

            if ($horizonStatus['status'] !== 'running') {
                $status = 'critical';
                $alerts[] = 'Horizon is not running';
            }

            if (count($activeWorkers) < $this->thresholds['min_workers'] ?? 1) {
                $status = 'critical';
                $alerts[] = 'Insufficient active workers';
            }

            return [
                'status' => $status,
                'horizon' => $horizonStatus,
                'active_workers' => $activeWorkers,
                'worker_count' => count($activeWorkers),
                'performance' => $workerPerformance,
                'alerts' => $alerts,
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Failed job'ları kontrol et
     */
    public function checkFailedJobs(): array
    {
        try {
            $failedJobCount = DB::table('failed_jobs')->count();
            $recentFailures = DB::table('failed_jobs')
                ->where('failed_at', '>=', now()->subHour())
                ->count();

            $threshold = $this->thresholds['failed_jobs']['total'] ?? 100;
            $recentThreshold = $this->thresholds['failed_jobs']['recent'] ?? 10;

            $status = 'healthy';
            $alerts = [];

            if ($failedJobCount > $threshold) {
                $status = 'warning';
                $alerts[] = "High number of failed jobs: {$failedJobCount}";
            }

            if ($recentFailures > $recentThreshold) {
                $status = 'critical';
                $alerts[] = "High recent failure rate: {$recentFailures} in last hour";
            }

            // Failed job kategorilerini analiz et
            $failuresByQueue = $this->analyzeFailuresByQueue();

            return [
                'status' => $status,
                'total_failed' => $failedJobCount,
                'recent_failures' => $recentFailures,
                'failure_rate' => $this->calculateFailureRate(),
                'failures_by_queue' => $failuresByQueue,
                'alerts' => $alerts,
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Job processing rate kontrolü
     */
    public function checkJobProcessingRate(): array
    {
        try {
            $cacheKey = 'queue_processing_rate';
            $currentRate = Cache::get($cacheKey, 0);
            
            // Son 5 dakikadaki işlenen job sayısını hesapla
            $recentProcessed = $this->getRecentProcessedJobCount();
            $processingRate = $recentProcessed / 5; // jobs per minute

            // Rate'i cache'e kaydet
            Cache::put($cacheKey, $processingRate, 300);

            $minRate = $this->thresholds['processing_rate']['min'] ?? 10;
            $maxRate = $this->thresholds['processing_rate']['max'] ?? 1000;

            $status = 'healthy';
            $alerts = [];

            if ($processingRate < $minRate) {
                $status = 'warning';
                $alerts[] = "Low processing rate: {$processingRate} jobs/min";
            } elseif ($processingRate > $maxRate) {
                $status = 'warning';
                $alerts[] = "Very high processing rate: {$processingRate} jobs/min";
            }

            return [
                'status' => $status,
                'current_rate' => round($processingRate, 2),
                'recent_processed' => $recentProcessed,
                'thresholds' => ['min' => $minRate, 'max' => $maxRate],
                'alerts' => $alerts,
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Memory usage kontrolü
     */
    public function checkMemoryUsage(): array
    {
        try {
            $memoryUsage = memory_get_usage(true);
            $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
            $memoryPercent = ($memoryUsage / $memoryLimit) * 100;

            $status = 'healthy';
            $alerts = [];

            if ($memoryPercent > 90) {
                $status = 'critical';
                $alerts[] = "Critical memory usage: {$memoryPercent}%";
            } elseif ($memoryPercent > 80) {
                $status = 'warning';
                $alerts[] = "High memory usage: {$memoryPercent}%";
            }

            return [
                'status' => $status,
                'current_usage' => $memoryUsage,
                'memory_limit' => $memoryLimit,
                'usage_percent' => round($memoryPercent, 2),
                'formatted_usage' => $this->formatBytes($memoryUsage),
                'formatted_limit' => $this->formatBytes($memoryLimit),
                'alerts' => $alerts,
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Response time kontrolü
     */
    public function checkResponseTimes(): array
    {
        // Bu method job processing response time'larını kontrol eder
        // Implementation cache'den veya monitoring sisteminden veri alabilir
        
        return [
            'status' => 'healthy',
            'avg_response_time' => 0,
            'p95_response_time' => 0,
            'p99_response_time' => 0,
        ];
    }

    /**
     * Dead letter queue kontrolü
     */
    public function checkDeadLetterQueue(): array
    {
        try {
            $redis = Redis::connection();
            $dlqSize = $redis->llen('queues:dead_letter');
            $threshold = $this->thresholds['dead_letter_queue'] ?? 50;

            $status = $dlqSize > $threshold ? 'warning' : 'healthy';
            $alerts = $dlqSize > $threshold ? ["Dead letter queue has {$dlqSize} jobs"] : [];

            return [
                'status' => $status,
                'size' => $dlqSize,
                'threshold' => $threshold,
                'alerts' => $alerts,
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Helper methods
     */
    protected function calculateOverallStatus(array $checks): string
    {
        $statuses = array_column($checks, 'status');
        
        if (in_array('critical', $statuses)) {
            return 'critical';
        } elseif (in_array('error', $statuses)) {
            return 'error';
        } elseif (in_array('warning', $statuses)) {
            return 'warning';
        } else {
            return 'healthy';
        }
    }

    protected function generateHealthSummary(array $checks): array
    {
        $summary = [
            'healthy_checks' => 0,
            'warning_checks' => 0,
            'critical_checks' => 0,
            'error_checks' => 0,
        ];

        foreach ($checks as $check) {
            $status = $check['status'];
            $summary["{$status}_checks"]++;
        }

        return $summary;
    }

    protected function generateRecommendations(array $checks): array
    {
        $recommendations = [];

        foreach ($checks as $checkName => $check) {
            if ($check['status'] === 'critical' || $check['status'] === 'warning') {
                $recommendations[] = $this->getRecommendationForCheck($checkName, $check);
            }
        }

        return array_filter($recommendations);
    }

    protected function getRecommendationForCheck(string $checkName, array $check): ?string
    {
        return match($checkName) {
            'queue_sizes' => 'Consider increasing worker capacity or optimizing job processing',
            'worker_status' => 'Check worker processes and restart if necessary',
            'failed_jobs' => 'Investigate failed jobs and fix underlying issues',
            'memory_usage' => 'Monitor memory leaks and consider increasing memory limits',
            default => null,
        };
    }

    protected function sendCriticalAlert(array $healthReport): void
    {
        Log::critical('Queue system critical health status', $healthReport);
        
        // Burada email, Slack, SMS gibi alert kanalları implement edilebilir
        foreach ($this->alertChannels as $channel) {
            $this->sendAlert($channel, $healthReport);
        }
    }

    protected function sendAlert(string $channel, array $data): void
    {
        // Alert channel implementation
        // Email, Slack, webhook, etc.
    }

    // Diğer helper methods...
    protected function determineConnectivityStatus(array $results): string
    {
        $healthyCount = count(array_filter($results, fn($r) => $r['status'] === 'healthy'));
        $totalCount = count($results);
        
        if ($healthyCount === 0) return 'critical';
        if ($healthyCount < $totalCount) return 'warning';
        return 'healthy';
    }

    protected function determineQueueSizeStatus(array $queueSizes): string
    {
        $statuses = array_column($queueSizes, 'status');
        
        if (in_array('critical', $statuses)) return 'critical';
        if (in_array('warning', $statuses)) return 'warning';
        return 'healthy';
    }

    protected function checkHorizonStatus(): array
    {
        // Horizon status check implementation
        return ['status' => 'running'];
    }

    protected function getActiveWorkers(): array
    {
        // Active workers implementation
        return [];
    }

    protected function checkWorkerPerformance(): array
    {
        // Worker performance check implementation
        return [];
    }

    protected function analyzeFailuresByQueue(): array
    {
        // Failure analysis implementation
        return [];
    }

    protected function calculateFailureRate(): float
    {
        // Failure rate calculation implementation
        return 0.0;
    }

    protected function getRecentProcessedJobCount(): int
    {
        // Recent processed job count implementation
        return 0;
    }

    protected function parseMemoryLimit(string $memoryLimit): int
    {
        $memoryLimit = trim($memoryLimit);
        $last = strtolower($memoryLimit[strlen($memoryLimit)-1]);
        $memoryLimit = (int) $memoryLimit;
        
        switch($last) {
            case 'g': $memoryLimit *= 1024;
            case 'm': $memoryLimit *= 1024;
            case 'k': $memoryLimit *= 1024;
        }
        
        return $memoryLimit;
    }

    protected function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
