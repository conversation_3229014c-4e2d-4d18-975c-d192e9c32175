<?php

namespace App\Core\Infrastructure\Queue\Services;

use App\Core\Infrastructure\Queue\Contracts\QueueAnalyticsInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;

/**
 * Queue Analytics Service
 * Queue performans analitikleri ve izleme servisi
 */
class QueueAnalyticsService implements QueueAnalyticsInterface
{
    protected array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'cache_ttl' => 300, // 5 minutes
            'metrics_retention_days' => 30,
            'sampling_rate' => 1.0, // %100 sampling
            'real_time_enabled' => true,
        ], $config);
    }

    /**
     * Queue genel istatistiklerini al
     */
    public function getQueueStatistics(string $queue = null): array
    {
        $cacheKey = "queue_stats:" . ($queue ?? 'all');
        
        return Cache::remember($cacheKey, $this->config['cache_ttl'], function () use ($queue) {
            $stats = [
                'total_jobs' => 0,
                'pending_jobs' => 0,
                'processing_jobs' => 0,
                'completed_jobs' => 0,
                'failed_jobs' => 0,
                'avg_processing_time' => 0,
                'throughput_per_minute' => 0,
                'success_rate' => 0,
                'queues' => [],
            ];

            // Database'den job istatistikleri
            $query = DB::table('jobs');
            if ($queue) {
                $query->where('queue', $queue);
            }

            $stats['pending_jobs'] = $query->count();

            // Failed jobs
            $failedQuery = DB::table('failed_jobs');
            if ($queue) {
                $failedQuery->where('queue', $queue);
            }
            $stats['failed_jobs'] = $failedQuery->count();

            // Redis'ten real-time veriler
            if ($this->config['real_time_enabled']) {
                $redisStats = $this->getRedisQueueStats($queue);
                $stats = array_merge($stats, $redisStats);
            }

            // Queue bazlı istatistikler
            $stats['queues'] = $this->getPerQueueStatistics();

            // Success rate hesaplama
            $totalProcessed = $stats['completed_jobs'] + $stats['failed_jobs'];
            if ($totalProcessed > 0) {
                $stats['success_rate'] = $stats['completed_jobs'] / $totalProcessed;
            }

            return $stats;
        });
    }

    /**
     * Job performans metriklerini al
     */
    public function getJobPerformanceMetrics(string $jobClass = null, array $options = []): array
    {
        $period = $options['period'] ?? 'last_24_hours';
        $cacheKey = "job_performance:" . md5($jobClass . $period);

        return Cache::remember($cacheKey, $this->config['cache_ttl'], function () use ($jobClass, $period) {
            $metrics = [
                'total_executions' => 0,
                'successful_executions' => 0,
                'failed_executions' => 0,
                'avg_execution_time' => 0,
                'min_execution_time' => 0,
                'max_execution_time' => 0,
                'avg_memory_usage' => 0,
                'retry_rate' => 0,
                'error_distribution' => [],
                'hourly_distribution' => [],
            ];

            $dateRange = $this->getDateRangeForPeriod($period);

            // Job events tablosundan metrikleri al
            $query = DB::table('queue_events')
                ->whereBetween('created_at', $dateRange);

            if ($jobClass) {
                $query->where('job_class', $jobClass);
            }

            $events = $query->get();

            foreach ($events as $event) {
                $metrics['total_executions']++;
                
                if ($event->status === 'completed') {
                    $metrics['successful_executions']++;
                } elseif ($event->status === 'failed') {
                    $metrics['failed_executions']++;
                }

                // Execution time metrics
                if ($event->execution_time) {
                    $execTime = $event->execution_time;
                    $metrics['avg_execution_time'] = 
                        ($metrics['avg_execution_time'] * ($metrics['total_executions'] - 1) + $execTime) 
                        / $metrics['total_executions'];
                    
                    $metrics['min_execution_time'] = $metrics['min_execution_time'] 
                        ? min($metrics['min_execution_time'], $execTime) 
                        : $execTime;
                    
                    $metrics['max_execution_time'] = max($metrics['max_execution_time'], $execTime);
                }

                // Memory usage
                if ($event->memory_usage) {
                    $metrics['avg_memory_usage'] = 
                        ($metrics['avg_memory_usage'] * ($metrics['total_executions'] - 1) + $event->memory_usage) 
                        / $metrics['total_executions'];
                }

                // Error distribution
                if ($event->status === 'failed' && $event->error_type) {
                    $errorType = $event->error_type;
                    $metrics['error_distribution'][$errorType] = 
                        ($metrics['error_distribution'][$errorType] ?? 0) + 1;
                }

                // Hourly distribution
                $hour = Carbon::parse($event->created_at)->format('H');
                $metrics['hourly_distribution'][$hour] = 
                    ($metrics['hourly_distribution'][$hour] ?? 0) + 1;
            }

            // Retry rate hesaplama
            if ($metrics['total_executions'] > 0) {
                $retryCount = DB::table('queue_events')
                    ->whereBetween('created_at', $dateRange)
                    ->where('event_type', 'retry')
                    ->when($jobClass, fn($q) => $q->where('job_class', $jobClass))
                    ->count();
                
                $metrics['retry_rate'] = $retryCount / $metrics['total_executions'];
            }

            return $metrics;
        });
    }

    /**
     * Queue throughput analizi
     */
    public function getThroughputAnalysis(string $queue = null, string $period = 'last_24_hours'): array
    {
        $cacheKey = "throughput_analysis:" . md5($queue . $period);

        return Cache::remember($cacheKey, $this->config['cache_ttl'], function () use ($queue, $period) {
            $dateRange = $this->getDateRangeForPeriod($period);
            
            $query = DB::table('queue_events')
                ->select(
                    DB::raw('DATE_FORMAT(created_at, "%Y-%m-%d %H:00:00") as hour'),
                    DB::raw('COUNT(*) as job_count'),
                    DB::raw('AVG(execution_time) as avg_execution_time'),
                    DB::raw('SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_count'),
                    DB::raw('SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_count')
                )
                ->whereBetween('created_at', $dateRange)
                ->when($queue, fn($q) => $q->where('queue', $queue))
                ->groupBy('hour')
                ->orderBy('hour')
                ->get();

            $throughput = [];
            $totalJobs = 0;
            $totalCompleted = 0;
            $totalFailed = 0;

            foreach ($query as $row) {
                $throughput[] = [
                    'timestamp' => $row->hour,
                    'jobs_per_hour' => $row->job_count,
                    'avg_execution_time' => round($row->avg_execution_time, 2),
                    'completed_jobs' => $row->completed_count,
                    'failed_jobs' => $row->failed_count,
                    'success_rate' => $row->job_count > 0 
                        ? round($row->completed_count / $row->job_count, 4) 
                        : 0,
                ];

                $totalJobs += $row->job_count;
                $totalCompleted += $row->completed_count;
                $totalFailed += $row->failed_count;
            }

            return [
                'period' => $period,
                'queue' => $queue,
                'hourly_throughput' => $throughput,
                'summary' => [
                    'total_jobs' => $totalJobs,
                    'total_completed' => $totalCompleted,
                    'total_failed' => $totalFailed,
                    'overall_success_rate' => $totalJobs > 0 ? $totalCompleted / $totalJobs : 0,
                    'avg_jobs_per_hour' => count($throughput) > 0 ? $totalJobs / count($throughput) : 0,
                ],
            ];
        });
    }

    /**
     * Queue sağlık durumunu kontrol et
     */
    public function getQueueHealthStatus(): array
    {
        $health = [
            'overall_status' => 'healthy',
            'issues' => [],
            'metrics' => [],
            'recommendations' => [],
        ];

        // Pending jobs kontrolü
        $pendingJobs = DB::table('jobs')->count();
        $health['metrics']['pending_jobs'] = $pendingJobs;

        if ($pendingJobs > 1000) {
            $health['overall_status'] = 'warning';
            $health['issues'][] = 'High number of pending jobs';
            $health['recommendations'][] = 'Consider scaling up workers';
        }

        // Failed jobs kontrolü
        $recentFailedJobs = DB::table('failed_jobs')
            ->where('failed_at', '>', Carbon::now()->subHour())
            ->count();
        
        $health['metrics']['recent_failed_jobs'] = $recentFailedJobs;

        if ($recentFailedJobs > 50) {
            $health['overall_status'] = 'critical';
            $health['issues'][] = 'High failure rate in last hour';
            $health['recommendations'][] = 'Investigate job failures';
        }

        // Worker durumu kontrolü
        $activeWorkers = $this->getActiveWorkerCount();
        $health['metrics']['active_workers'] = $activeWorkers;

        if ($activeWorkers === 0) {
            $health['overall_status'] = 'critical';
            $health['issues'][] = 'No active workers detected';
            $health['recommendations'][] = 'Start queue workers immediately';
        }

        // Memory usage kontrolü
        $avgMemoryUsage = $this->getAverageMemoryUsage();
        $health['metrics']['avg_memory_usage_mb'] = round($avgMemoryUsage / 1024 / 1024, 2);

        if ($avgMemoryUsage > 512 * 1024 * 1024) { // 512MB
            $health['overall_status'] = 'warning';
            $health['issues'][] = 'High memory usage detected';
            $health['recommendations'][] = 'Monitor for memory leaks';
        }

        return $health;
    }

    /**
     * Job analitiklerini al
     */
    public function getJobAnalytics(string $jobClass, array $options = []): array
    {
        $period = $options['period'] ?? 'last_24_hours';
        $metrics = $options['metrics'] ?? ['all'];

        $analytics = [
            'job_class' => $jobClass,
            'period' => $period,
        ];

        if (in_array('all', $metrics) || in_array('success_rate', $metrics)) {
            $analytics['success_rate'] = $this->calculateJobSuccessRate($jobClass, $period);
        }

        if (in_array('all', $metrics) || in_array('avg_retry_count', $metrics)) {
            $analytics['avg_retry_count'] = $this->calculateAverageRetryCount($jobClass, $period);
        }

        if (in_array('all', $metrics) || in_array('failure_patterns', $metrics)) {
            $analytics['failure_patterns'] = $this->analyzeFailurePatterns($jobClass, $period);
        }

        if (in_array('all', $metrics) || in_array('retry_stats', $metrics)) {
            $analytics['retry_stats'] = $this->getRetryStatistics($jobClass, $period);
        }

        return $analytics;
    }

    /**
     * Event kaydet
     */
    public function recordEvent(string $eventType, array $data): void
    {
        // Sampling kontrolü
        if (mt_rand() / mt_getrandmax() > $this->config['sampling_rate']) {
            return;
        }

        DB::table('queue_events')->insert([
            'event_type' => $eventType,
            'queue' => $data['queue'] ?? null,
            'job_class' => $data['job_class'] ?? null,
            'job_id' => $data['job_id'] ?? null,
            'status' => $data['status'] ?? null,
            'execution_time' => $data['execution_time'] ?? null,
            'memory_usage' => $data['memory_usage'] ?? null,
            'error_type' => $data['error_type'] ?? null,
            'error_message' => $data['error_message'] ?? null,
            'metadata' => json_encode($data['metadata'] ?? []),
            'created_at' => now(),
        ]);
    }

    /**
     * Redis'ten queue istatistikleri al
     */
    protected function getRedisQueueStats(string $queue = null): array
    {
        $stats = [
            'processing_jobs' => 0,
            'completed_jobs' => 0,
        ];

        try {
            $redis = Redis::connection();
            
            if ($queue) {
                $stats['processing_jobs'] = $redis->llen("queues:{$queue}:reserved");
            } else {
                // Tüm queue'lar için toplam
                $queues = ['default', 'emails', 'orders', 'realtime', 'cache'];
                foreach ($queues as $q) {
                    $stats['processing_jobs'] += $redis->llen("queues:{$q}:reserved");
                }
            }

        } catch (\Exception $e) {
            // Redis bağlantı hatası durumunda sessizce devam et
        }

        return $stats;
    }

    /**
     * Queue bazlı istatistikler
     */
    protected function getPerQueueStatistics(): array
    {
        $queues = ['default', 'emails', 'orders', 'realtime', 'cache', 'notifications'];
        $stats = [];

        foreach ($queues as $queue) {
            $stats[$queue] = [
                'pending_jobs' => DB::table('jobs')->where('queue', $queue)->count(),
                'failed_jobs' => DB::table('failed_jobs')->where('queue', $queue)->count(),
            ];
        }

        return $stats;
    }

    /**
     * Period için tarih aralığı al
     */
    protected function getDateRangeForPeriod(string $period): array
    {
        return match ($period) {
            'last_hour' => [Carbon::now()->subHour(), Carbon::now()],
            'last_24_hours' => [Carbon::now()->subDay(), Carbon::now()],
            'last_7_days' => [Carbon::now()->subWeek(), Carbon::now()],
            'last_30_days' => [Carbon::now()->subMonth(), Carbon::now()],
            default => [Carbon::now()->subDay(), Carbon::now()],
        };
    }

    /**
     * Aktif worker sayısını al
     */
    protected function getActiveWorkerCount(): int
    {
        // Horizon kullanılıyorsa Horizon API'den al
        try {
            $redis = Redis::connection();
            $workers = $redis->smembers('horizon:workers');
            return count($workers);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Ortalama memory usage al
     */
    protected function getAverageMemoryUsage(): float
    {
        return DB::table('queue_events')
            ->where('created_at', '>', Carbon::now()->subHour())
            ->whereNotNull('memory_usage')
            ->avg('memory_usage') ?? 0;
    }

    /**
     * Job başarı oranını hesapla
     */
    protected function calculateJobSuccessRate(string $jobClass, string $period): float
    {
        $dateRange = $this->getDateRangeForPeriod($period);
        
        $total = DB::table('queue_events')
            ->where('job_class', $jobClass)
            ->whereBetween('created_at', $dateRange)
            ->whereIn('status', ['completed', 'failed'])
            ->count();

        if ($total === 0) {
            return 1.0;
        }

        $successful = DB::table('queue_events')
            ->where('job_class', $jobClass)
            ->whereBetween('created_at', $dateRange)
            ->where('status', 'completed')
            ->count();

        return $successful / $total;
    }

    /**
     * Ortalama retry sayısını hesapla
     */
    protected function calculateAverageRetryCount(string $jobClass, string $period): float
    {
        $dateRange = $this->getDateRangeForPeriod($period);
        
        return DB::table('queue_events')
            ->where('job_class', $jobClass)
            ->whereBetween('created_at', $dateRange)
            ->where('event_type', 'retry')
            ->avg('metadata->retry_count') ?? 0;
    }

    /**
     * Hata desenlerini analiz et
     */
    protected function analyzeFailurePatterns(string $jobClass, string $period): array
    {
        $dateRange = $this->getDateRangeForPeriod($period);
        
        return DB::table('queue_events')
            ->select('error_type', DB::raw('COUNT(*) as count'))
            ->where('job_class', $jobClass)
            ->whereBetween('created_at', $dateRange)
            ->where('status', 'failed')
            ->groupBy('error_type')
            ->orderByDesc('count')
            ->get()
            ->toArray();
    }

    /**
     * Retry istatistiklerini al
     */
    protected function getRetryStatistics(string $jobClass, string $period): array
    {
        $dateRange = $this->getDateRangeForPeriod($period);

        $retryEvents = DB::table('queue_events')
            ->where('job_class', $jobClass)
            ->whereBetween('created_at', $dateRange)
            ->where('event_type', 'retry')
            ->get();

        $stats = [
            'total_retries' => $retryEvents->count(),
            'successful_retries' => 0,
            'failed_retries' => 0,
            'avg_retry_delay' => 0,
        ];

        foreach ($retryEvents as $event) {
            $metadata = json_decode($event->metadata, true) ?? [];

            if (isset($metadata['final_outcome'])) {
                if ($metadata['final_outcome'] === 'success') {
                    $stats['successful_retries']++;
                } else {
                    $stats['failed_retries']++;
                }
            }

            if (isset($metadata['retry_delay'])) {
                $stats['avg_retry_delay'] += $metadata['retry_delay'];
            }
        }

        if ($stats['total_retries'] > 0) {
            $stats['avg_retry_delay'] /= $stats['total_retries'];
        }

        return $stats;
    }

    /**
     * Queue trend analizi
     */
    public function getQueueTrends(string $queue = null, string $period = 'last_7_days'): array
    {
        $dateRange = $this->getDateRangeForPeriod($period);

        $trends = DB::table('queue_performance_metrics')
            ->select(
                'metric_date',
                DB::raw('SUM(total_jobs) as total_jobs'),
                DB::raw('SUM(completed_jobs) as completed_jobs'),
                DB::raw('SUM(failed_jobs) as failed_jobs'),
                DB::raw('AVG(success_rate) as avg_success_rate'),
                DB::raw('AVG(avg_execution_time) as avg_execution_time'),
                DB::raw('AVG(jobs_per_hour) as avg_throughput')
            )
            ->whereBetween('metric_date', [
                $dateRange[0]->format('Y-m-d'),
                $dateRange[1]->format('Y-m-d')
            ])
            ->when($queue, fn($q) => $q->where('queue', $queue))
            ->where('metric_period', 'day')
            ->groupBy('metric_date')
            ->orderBy('metric_date')
            ->get();

        return [
            'period' => $period,
            'queue' => $queue,
            'trends' => $trends->toArray(),
            'summary' => [
                'total_days' => $trends->count(),
                'avg_daily_jobs' => $trends->avg('total_jobs'),
                'avg_success_rate' => $trends->avg('avg_success_rate'),
                'trend_direction' => $this->calculateTrendDirection($trends),
            ],
        ];
    }

    /**
     * Trend yönünü hesapla
     */
    protected function calculateTrendDirection($trends): string
    {
        if ($trends->count() < 2) {
            return 'insufficient_data';
        }

        $first = $trends->first();
        $last = $trends->last();

        $jobsTrend = $last->total_jobs - $first->total_jobs;
        $successTrend = $last->avg_success_rate - $first->avg_success_rate;

        if ($jobsTrend > 0 && $successTrend >= 0) {
            return 'improving';
        } elseif ($jobsTrend < 0 && $successTrend < 0) {
            return 'declining';
        } else {
            return 'stable';
        }
    }
}
