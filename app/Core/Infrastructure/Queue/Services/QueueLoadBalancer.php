<?php

namespace App\Core\Infrastructure\Queue\Services;

use App\Core\Infrastructure\Queue\Contracts\QueueLoadBalancerInterface;
use App\Core\Infrastructure\Queue\Contracts\QueueAnalyticsInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;

/**
 * Queue Load Balancer
 * Queue yük dengeleme ve optimizasyon servisi
 */
class QueueLoadBalancer implements QueueLoadBalancerInterface
{
    protected QueueAnalyticsInterface $analytics;
    protected array $config;
    protected array $queueWeights;

    public function __construct(QueueAnalyticsInterface $analytics, array $config = [])
    {
        $this->analytics = $analytics;
        $this->config = array_merge([
            'enabled' => true,
            'rebalance_interval' => 300, // 5 minutes
            'load_threshold' => 0.8, // 80%
            'min_workers_per_queue' => 1,
            'max_workers_per_queue' => 10,
            'auto_scaling_enabled' => true,
            'scaling_factor' => 1.5,
        ], $config);

        $this->queueWeights = [
            'realtime' => 10,    // Highest priority
            'orders' => 8,       // High priority
            'emails' => 6,       // Medium priority
            'notifications' => 5, // Medium priority
            'cache' => 4,        // Low priority
            'default' => 3,      // Lowest priority
        ];
    }

    /**
     * Queue yük dengeleme gerçekleştir
     */
    public function balanceLoad(): array
    {
        if (!$this->config['enabled']) {
            return ['status' => 'disabled'];
        }

        $startTime = microtime(true);
        $balancingResult = [
            'timestamp' => now(),
            'actions_taken' => [],
            'queue_analysis' => [],
            'worker_adjustments' => [],
            'recommendations' => [],
        ];

        try {
            // Queue analizi
            $queueAnalysis = $this->analyzeQueueLoad();
            $balancingResult['queue_analysis'] = $queueAnalysis;

            // Yük dengeleme stratejisi belirle
            $strategy = $this->determineBalancingStrategy($queueAnalysis);
            $balancingResult['strategy'] = $strategy;

            // Yük dengeleme işlemlerini gerçekleştir
            $actions = $this->executeBalancingStrategy($strategy, $queueAnalysis);
            $balancingResult['actions_taken'] = $actions;

            // Worker ayarlamaları
            if ($this->config['auto_scaling_enabled']) {
                $workerAdjustments = $this->adjustWorkerAllocation($queueAnalysis);
                $balancingResult['worker_adjustments'] = $workerAdjustments;
            }

            // Öneriler oluştur
            $recommendations = $this->generateRecommendations($queueAnalysis);
            $balancingResult['recommendations'] = $recommendations;

            $duration = microtime(true) - $startTime;
            $balancingResult['duration'] = $duration;
            $balancingResult['status'] = 'success';

            // Sonuçları kaydet
            $this->recordBalancingResult($balancingResult);

        } catch (\Exception $e) {
            $balancingResult['status'] = 'error';
            $balancingResult['error'] = $e->getMessage();
            $balancingResult['duration'] = microtime(true) - $startTime;
        }

        return $balancingResult;
    }

    /**
     * Queue yükünü analiz et
     */
    protected function analyzeQueueLoad(): array
    {
        $analysis = [
            'queues' => [],
            'total_load' => 0,
            'average_load' => 0,
            'load_distribution' => [],
            'bottlenecks' => [],
        ];

        $queues = ['realtime', 'orders', 'emails', 'notifications', 'cache', 'default'];
        $totalJobs = 0;
        $totalWeight = 0;

        foreach ($queues as $queue) {
            $queueStats = $this->analytics->getQueueStatistics($queue);
            $queueLoad = $this->calculateQueueLoad($queue, $queueStats);
            
            $analysis['queues'][$queue] = [
                'pending_jobs' => $queueStats['pending_jobs'],
                'processing_jobs' => $queueStats['processing_jobs'],
                'failed_jobs' => $queueStats['failed_jobs'],
                'success_rate' => $queueStats['success_rate'],
                'avg_processing_time' => $queueStats['avg_processing_time'],
                'load_score' => $queueLoad['load_score'],
                'utilization' => $queueLoad['utilization'],
                'weight' => $this->queueWeights[$queue] ?? 1,
                'status' => $queueLoad['status'],
                'worker_count' => $this->getQueueWorkerCount($queue),
            ];

            $totalJobs += $queueStats['pending_jobs'];
            $totalWeight += ($this->queueWeights[$queue] ?? 1);

            // Bottleneck tespiti
            if ($queueLoad['status'] === 'overloaded') {
                $analysis['bottlenecks'][] = [
                    'queue' => $queue,
                    'reason' => 'overloaded',
                    'load_score' => $queueLoad['load_score'],
                    'pending_jobs' => $queueStats['pending_jobs'],
                ];
            }
        }

        $analysis['total_load'] = $totalJobs;
        $analysis['average_load'] = $totalWeight > 0 ? $totalJobs / $totalWeight : 0;
        $analysis['load_distribution'] = $this->calculateLoadDistribution($analysis['queues']);

        return $analysis;
    }

    /**
     * Queue yükünü hesapla
     */
    protected function calculateQueueLoad(string $queue, array $stats): array
    {
        $pendingJobs = $stats['pending_jobs'];
        $processingJobs = $stats['processing_jobs'];
        $avgProcessingTime = $stats['avg_processing_time'];
        $workerCount = $this->getQueueWorkerCount($queue);

        // Load score hesaplama
        $loadScore = 0;
        
        // Pending jobs factor
        $loadScore += $pendingJobs * 0.4;
        
        // Processing time factor
        $loadScore += ($avgProcessingTime / 60) * $processingJobs * 0.3;
        
        // Worker efficiency factor
        if ($workerCount > 0) {
            $jobsPerWorker = ($pendingJobs + $processingJobs) / $workerCount;
            $loadScore += $jobsPerWorker * 0.3;
        } else {
            $loadScore += $pendingJobs; // No workers = high load
        }

        // Utilization hesaplama
        $maxCapacity = $workerCount * 10; // Assume 10 jobs per worker capacity
        $utilization = $maxCapacity > 0 ? (($pendingJobs + $processingJobs) / $maxCapacity) : 1;

        // Status belirleme
        $status = 'normal';
        if ($utilization > $this->config['load_threshold']) {
            $status = 'overloaded';
        } elseif ($utilization > 0.6) {
            $status = 'high';
        } elseif ($utilization < 0.2) {
            $status = 'underutilized';
        }

        return [
            'load_score' => round($loadScore, 2),
            'utilization' => round($utilization, 3),
            'status' => $status,
        ];
    }

    /**
     * Yük dengeleme stratejisi belirle
     */
    protected function determineBalancingStrategy(array $analysis): array
    {
        $strategy = [
            'type' => 'maintain',
            'priority_adjustments' => [],
            'worker_redistributions' => [],
            'job_redistributions' => [],
        ];

        // Bottleneck varsa aggressive strategy
        if (!empty($analysis['bottlenecks'])) {
            $strategy['type'] = 'aggressive';
            
            foreach ($analysis['bottlenecks'] as $bottleneck) {
                $queue = $bottleneck['queue'];
                
                // Worker redistribution
                $strategy['worker_redistributions'][] = [
                    'from_queue' => $this->findUnderutilizedQueue($analysis),
                    'to_queue' => $queue,
                    'worker_count' => $this->calculateWorkerRedistribution($queue, $analysis),
                ];
                
                // Priority adjustment
                $strategy['priority_adjustments'][] = [
                    'queue' => $queue,
                    'action' => 'increase_priority',
                    'factor' => 1.5,
                ];
            }
        }
        // Yük dağılımı dengesizse moderate strategy
        elseif ($this->isLoadImbalanced($analysis)) {
            $strategy['type'] = 'moderate';
            $strategy['job_redistributions'] = $this->planJobRedistribution($analysis);
        }

        return $strategy;
    }

    /**
     * Yük dengeleme stratejisini uygula
     */
    protected function executeBalancingStrategy(array $strategy, array $analysis): array
    {
        $actions = [];

        switch ($strategy['type']) {
            case 'aggressive':
                $actions = array_merge($actions, $this->executeAggressiveBalancing($strategy));
                break;
                
            case 'moderate':
                $actions = array_merge($actions, $this->executeModerateBalancing($strategy));
                break;
                
            case 'maintain':
            default:
                $actions[] = [
                    'type' => 'maintain',
                    'message' => 'Load is balanced, no action needed',
                ];
                break;
        }

        return $actions;
    }

    /**
     * Aggressive balancing uygula
     */
    protected function executeAggressiveBalancing(array $strategy): array
    {
        $actions = [];

        // Worker redistribution
        foreach ($strategy['worker_redistributions'] as $redistribution) {
            if ($redistribution['from_queue'] && $redistribution['worker_count'] > 0) {
                $action = $this->redistributeWorkers(
                    $redistribution['from_queue'],
                    $redistribution['to_queue'],
                    $redistribution['worker_count']
                );
                $actions[] = $action;
            }
        }

        // Priority adjustments
        foreach ($strategy['priority_adjustments'] as $adjustment) {
            $action = $this->adjustQueuePriority(
                $adjustment['queue'],
                $adjustment['factor']
            );
            $actions[] = $action;
        }

        return $actions;
    }

    /**
     * Moderate balancing uygula
     */
    protected function executeModerateBalancing(array $strategy): array
    {
        $actions = [];

        // Job redistribution
        foreach ($strategy['job_redistributions'] as $redistribution) {
            $action = $this->redistributeJobs(
                $redistribution['from_queue'],
                $redistribution['to_queue'],
                $redistribution['job_count']
            );
            $actions[] = $action;
        }

        return $actions;
    }

    /**
     * Worker'ları yeniden dağıt
     */
    protected function redistributeWorkers(string $fromQueue, string $toQueue, int $workerCount): array
    {
        // Bu implementasyon Horizon/Supervisor ile entegre edilebilir
        // Şimdilik simulated action
        
        return [
            'type' => 'worker_redistribution',
            'from_queue' => $fromQueue,
            'to_queue' => $toQueue,
            'worker_count' => $workerCount,
            'status' => 'simulated', // 'completed' olabilir gerçek implementasyonda
            'message' => "Redistributed {$workerCount} workers from {$fromQueue} to {$toQueue}",
        ];
    }

    /**
     * Queue önceliğini ayarla
     */
    protected function adjustQueuePriority(string $queue, float $factor): array
    {
        $currentWeight = $this->queueWeights[$queue] ?? 1;
        $newWeight = (int) ($currentWeight * $factor);
        
        // Cache'e yeni weight'i kaydet
        Cache::put("queue_weight:{$queue}", $newWeight, 3600);
        
        return [
            'type' => 'priority_adjustment',
            'queue' => $queue,
            'old_weight' => $currentWeight,
            'new_weight' => $newWeight,
            'factor' => $factor,
            'status' => 'completed',
            'message' => "Adjusted {$queue} priority from {$currentWeight} to {$newWeight}",
        ];
    }

    /**
     * Job'ları yeniden dağıt
     */
    protected function redistributeJobs(string $fromQueue, string $toQueue, int $jobCount): array
    {
        // Bu implementasyon gerçek job redistribution logic'i içerebilir
        // Şimdilik simulated action
        
        return [
            'type' => 'job_redistribution',
            'from_queue' => $fromQueue,
            'to_queue' => $toQueue,
            'job_count' => $jobCount,
            'status' => 'simulated',
            'message' => "Redistributed {$jobCount} jobs from {$fromQueue} to {$toQueue}",
        ];
    }

    /**
     * Worker allocation'ını ayarla
     */
    protected function adjustWorkerAllocation(array $analysis): array
    {
        $adjustments = [];

        foreach ($analysis['queues'] as $queue => $queueData) {
            $currentWorkers = $queueData['worker_count'];
            $optimalWorkers = $this->calculateOptimalWorkerCount($queue, $queueData);
            
            if ($optimalWorkers !== $currentWorkers) {
                $adjustments[] = [
                    'queue' => $queue,
                    'current_workers' => $currentWorkers,
                    'optimal_workers' => $optimalWorkers,
                    'adjustment' => $optimalWorkers - $currentWorkers,
                    'reason' => $this->getWorkerAdjustmentReason($queueData),
                ];
            }
        }

        return $adjustments;
    }

    /**
     * Optimal worker sayısını hesapla
     */
    protected function calculateOptimalWorkerCount(string $queue, array $queueData): int
    {
        $pendingJobs = $queueData['pending_jobs'];
        $avgProcessingTime = $queueData['avg_processing_time'];
        $currentWorkers = $queueData['worker_count'];
        
        // Basit hesaplama: pending jobs ve processing time'a göre
        $estimatedOptimal = max(1, ceil($pendingJobs / 10)); // 10 job per worker
        
        // Processing time factor
        if ($avgProcessingTime > 60) { // 1 minute
            $estimatedOptimal = (int) ($estimatedOptimal * $this->config['scaling_factor']);
        }
        
        // Min/max sınırları
        $estimatedOptimal = max($this->config['min_workers_per_queue'], $estimatedOptimal);
        $estimatedOptimal = min($this->config['max_workers_per_queue'], $estimatedOptimal);
        
        return $estimatedOptimal;
    }

    /**
     * Worker adjustment sebebini belirle
     */
    protected function getWorkerAdjustmentReason(array $queueData): string
    {
        if ($queueData['status'] === 'overloaded') {
            return 'Queue overloaded, scaling up';
        } elseif ($queueData['status'] === 'underutilized') {
            return 'Queue underutilized, scaling down';
        } elseif ($queueData['avg_processing_time'] > 60) {
            return 'Long processing time, scaling up';
        } else {
            return 'Load balancing optimization';
        }
    }

    /**
     * Öneriler oluştur
     */
    protected function generateRecommendations(array $analysis): array
    {
        $recommendations = [];

        // Bottleneck önerileri
        foreach ($analysis['bottlenecks'] as $bottleneck) {
            $recommendations[] = "Consider scaling up workers for {$bottleneck['queue']} queue";
            $recommendations[] = "Investigate slow jobs in {$bottleneck['queue']} queue";
        }

        // Genel öneriler
        if ($analysis['average_load'] > 100) {
            $recommendations[] = "Overall system load is high, consider adding more workers";
        }

        // Underutilized queue'lar
        foreach ($analysis['queues'] as $queue => $data) {
            if ($data['status'] === 'underutilized' && $data['worker_count'] > 1) {
                $recommendations[] = "Consider reducing workers for {$queue} queue";
            }
        }

        return $recommendations;
    }

    /**
     * Helper methods
     */
    protected function getQueueWorkerCount(string $queue): int
    {
        try {
            return DB::table('queue_worker_stats')
                ->where('queue', $queue)
                ->where('status', 'active')
                ->where('last_activity', '>', Carbon::now()->subMinutes(5))
                ->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    protected function findUnderutilizedQueue(array $analysis): ?string
    {
        foreach ($analysis['queues'] as $queue => $data) {
            if ($data['status'] === 'underutilized' && $data['worker_count'] > 1) {
                return $queue;
            }
        }
        return null;
    }

    protected function calculateWorkerRedistribution(string $queue, array $analysis): int
    {
        $queueData = $analysis['queues'][$queue];
        $pendingJobs = $queueData['pending_jobs'];
        
        // Basit hesaplama: her 20 pending job için 1 worker
        return min(3, max(1, ceil($pendingJobs / 20)));
    }

    protected function isLoadImbalanced(array $analysis): bool
    {
        $loads = array_column($analysis['queues'], 'load_score');
        $maxLoad = max($loads);
        $minLoad = min($loads);
        
        // %50'den fazla fark varsa imbalanced
        return ($maxLoad - $minLoad) / max($maxLoad, 1) > 0.5;
    }

    protected function planJobRedistribution(array $analysis): array
    {
        // Job redistribution planning logic
        return [];
    }

    protected function calculateLoadDistribution(array $queues): array
    {
        $distribution = [];
        $totalLoad = array_sum(array_column($queues, 'load_score'));
        
        foreach ($queues as $queue => $data) {
            $distribution[$queue] = $totalLoad > 0 
                ? round(($data['load_score'] / $totalLoad) * 100, 2)
                : 0;
        }
        
        return $distribution;
    }

    protected function recordBalancingResult(array $result): void
    {
        // Balancing sonuçlarını database'e kaydet
        try {
            DB::table('queue_load_balancing_logs')->insert([
                'timestamp' => $result['timestamp'],
                'strategy_type' => $result['strategy']['type'] ?? 'unknown',
                'actions_taken' => json_encode($result['actions_taken']),
                'worker_adjustments' => json_encode($result['worker_adjustments']),
                'queue_analysis' => json_encode($result['queue_analysis']),
                'duration' => $result['duration'],
                'status' => $result['status'],
                'created_at' => now(),
            ]);
        } catch (\Exception $e) {
            // Log error but don't fail the balancing process
        }
    }
}
