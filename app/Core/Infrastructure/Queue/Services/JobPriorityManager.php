<?php

namespace App\Core\Infrastructure\Queue\Services;

use App\Core\Infrastructure\Queue\Contracts\PrioritizableJobInterface;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * Job Priority Manager
 * Job önceliklendirme ve routing yönetimi
 */
class JobPriorityManager
{
    protected array $config;
    protected array $queueMappings;
    protected array $priorityThresholds;

    public function __construct()
    {
        $this->config = config('queue_infrastructure.priority_management', []);
        $this->queueMappings = $this->config['queue_mappings'] ?? [];
        $this->priorityThresholds = $this->config['priority_thresholds'] ?? [];
    }

    /**
     * Job'ı önceliğine göre uygun queue'ya route et
     */
    public function routeJob($job): string
    {
        if (!$job instanceof PrioritizableJobInterface) {
            return $this->getDefaultQueue();
        }

        $priority = $job->calculateDynamicPriority($this->getSystemContext());
        $category = $job->getJobCategory();
        $businessImpact = $job->getBusinessImpact();
        $sla = $job->getSLA();

        // SLA-based routing
        if ($sla <= 30) { // 30 saniye altı critical
            return $this->getCriticalQueue($category);
        }

        // Priority-based routing
        $queueName = $this->determineQueueByPriority($priority, $category, $businessImpact);
        
        // Queue capacity kontrolü
        if ($this->isQueueOverloaded($queueName)) {
            $queueName = $this->findAlternativeQueue($queueName, $priority);
        }

        Log::debug('Job routed to queue', [
            'job_class' => get_class($job),
            'priority' => $priority,
            'category' => $category,
            'business_impact' => $businessImpact,
            'sla' => $sla,
            'queue' => $queueName,
        ]);

        return $queueName;
    }

    /**
     * Job'ın dinamik önceliğini hesapla
     */
    public function calculateDynamicPriority(PrioritizableJobInterface $job): int
    {
        $basePriority = $job->getPriority();
        $context = $this->getSystemContext();
        
        // System load factor
        $loadFactor = $this->calculateLoadFactor();
        
        // Time-based factor (rush hours)
        $timeFactor = $this->calculateTimeFactor();
        
        // Business impact factor
        $businessFactor = $this->calculateBusinessImpactFactor($job->getBusinessImpact());
        
        // SLA urgency factor
        $slaFactor = $this->calculateSLAFactor($job->getSLA());
        
        // Deadline factor
        $deadlineFactor = $this->calculateDeadlineFactor($job->getDeadline());

        // Weighted calculation
        $dynamicPriority = $basePriority;
        $dynamicPriority += ($loadFactor * 0.2);
        $dynamicPriority += ($timeFactor * 0.1);
        $dynamicPriority += ($businessFactor * 0.3);
        $dynamicPriority += ($slaFactor * 0.3);
        $dynamicPriority += ($deadlineFactor * 0.1);

        // Normalize to 1-10 range
        return max(1, min(10, round($dynamicPriority)));
    }

    /**
     * Queue capacity'sini kontrol et
     */
    public function isQueueOverloaded(string $queueName): bool
    {
        try {
            $redis = Redis::connection();
            $queueSize = $redis->llen("queues:{$queueName}");
            $threshold = $this->config['capacity_thresholds'][$queueName] ?? 1000;
            
            return $queueSize > $threshold;
        } catch (\Exception $e) {
            Log::warning('Failed to check queue capacity', [
                'queue' => $queueName,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Alternatif queue bul
     */
    public function findAlternativeQueue(string $originalQueue, int $priority): string
    {
        $alternatives = $this->config['alternative_queues'][$originalQueue] ?? [];
        
        foreach ($alternatives as $alternative) {
            if (!$this->isQueueOverloaded($alternative)) {
                Log::info('Job redirected to alternative queue', [
                    'original_queue' => $originalQueue,
                    'alternative_queue' => $alternative,
                    'priority' => $priority,
                ]);
                return $alternative;
            }
        }
        
        // Hiçbir alternatif yoksa orijinal queue'yu kullan
        return $originalQueue;
    }

    /**
     * Job escalation kontrolü
     */
    public function checkJobEscalation(PrioritizableJobInterface $job, int $waitTime): bool
    {
        $escalationRules = $job->getEscalationRules();
        
        foreach ($escalationRules as $rule) {
            if ($waitTime >= $rule['threshold']) {
                $this->escalateJob($job, $rule);
                return true;
            }
        }
        
        return false;
    }

    /**
     * Job'ı escalate et
     */
    protected function escalateJob(PrioritizableJobInterface $job, array $rule): void
    {
        $newPriority = max(1, $job->getPriority() - $rule['priority_boost']);
        $newQueue = $this->determineQueueByPriority($newPriority, $job->getJobCategory(), $job->getBusinessImpact());
        
        Log::warning('Job escalated due to long wait time', [
            'job_class' => get_class($job),
            'old_priority' => $job->getPriority(),
            'new_priority' => $newPriority,
            'new_queue' => $newQueue,
            'wait_time' => $rule['threshold'],
            'escalation_action' => $rule['action'],
        ]);

        // Escalation action'ını gerçekleştir
        $this->executeEscalationAction($job, $rule);
    }

    /**
     * System context'i al
     */
    protected function getSystemContext(): array
    {
        return Cache::remember('system_context', 60, function () {
            return [
                'current_load' => $this->getCurrentSystemLoad(),
                'active_jobs' => $this->getActiveJobCount(),
                'failed_jobs' => $this->getFailedJobCount(),
                'queue_sizes' => $this->getAllQueueSizes(),
                'time_of_day' => now()->hour,
                'day_of_week' => now()->dayOfWeek,
            ];
        });
    }

    /**
     * Load factor hesapla
     */
    protected function calculateLoadFactor(): float
    {
        $context = $this->getSystemContext();
        $currentLoad = $context['current_load'] ?? 0;
        
        // Yüksek load'da priority artır (daha düşük sayı = yüksek priority)
        if ($currentLoad > 80) {
            return -1; // Priority artır
        } elseif ($currentLoad > 60) {
            return -0.5;
        } elseif ($currentLoad < 20) {
            return 0.5; // Priority azalt
        }
        
        return 0;
    }

    /**
     * Time factor hesapla (rush hours)
     */
    protected function calculateTimeFactor(): float
    {
        $hour = now()->hour;
        $dayOfWeek = now()->dayOfWeek;
        
        // İş saatleri (9-17) ve hafta içi
        if ($dayOfWeek >= 1 && $dayOfWeek <= 5 && $hour >= 9 && $hour <= 17) {
            return -0.5; // Rush hours'da priority artır
        }
        
        return 0;
    }

    /**
     * Business impact factor hesapla
     */
    protected function calculateBusinessImpactFactor(string $businessImpact): float
    {
        return match($businessImpact) {
            'critical' => -2,
            'high' => -1,
            'medium' => 0,
            'low' => 1,
            default => 0,
        };
    }

    /**
     * SLA factor hesapla
     */
    protected function calculateSLAFactor(int $sla): float
    {
        if ($sla <= 30) return -2;      // 30 saniye altı
        if ($sla <= 60) return -1;      // 1 dakika altı
        if ($sla <= 300) return 0;      // 5 dakika altı
        if ($sla <= 900) return 0.5;    // 15 dakika altı
        return 1;                       // 15 dakika üstü
    }

    /**
     * Deadline factor hesapla
     */
    protected function calculateDeadlineFactor(?\DateTimeInterface $deadline): float
    {
        if (!$deadline) {
            return 0;
        }
        
        $timeToDeadline = $deadline->getTimestamp() - time();
        
        if ($timeToDeadline <= 300) return -2;     // 5 dakika kaldı
        if ($timeToDeadline <= 900) return -1;     // 15 dakika kaldı
        if ($timeToDeadline <= 3600) return 0;     // 1 saat kaldı
        return 0.5;                                // 1 saat üstü
    }

    /**
     * Priority'ye göre queue belirle
     */
    protected function determineQueueByPriority(int $priority, string $category, string $businessImpact): string
    {
        // Critical business impact için özel routing
        if ($businessImpact === 'critical') {
            return $this->getCriticalQueue($category);
        }
        
        // Priority-based mapping
        if ($priority <= 2) {
            return $this->getHighPriorityQueue($category);
        } elseif ($priority <= 5) {
            return $this->getMediumPriorityQueue($category);
        } else {
            return $this->getLowPriorityQueue($category);
        }
    }

    /**
     * Helper methods for queue selection
     */
    protected function getCriticalQueue(string $category): string
    {
        return $this->queueMappings['critical'][$category] ?? 'critical';
    }

    protected function getHighPriorityQueue(string $category): string
    {
        return $this->queueMappings['high'][$category] ?? 'high';
    }

    protected function getMediumPriorityQueue(string $category): string
    {
        return $this->queueMappings['medium'][$category] ?? 'default';
    }

    protected function getLowPriorityQueue(string $category): string
    {
        return $this->queueMappings['low'][$category] ?? 'low';
    }

    protected function getDefaultQueue(): string
    {
        return $this->config['default_queue'] ?? 'default';
    }

    /**
     * System metrics helper methods
     */
    protected function getCurrentSystemLoad(): float
    {
        // CPU load average'ı al (Linux/Unix)
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return $load[0] * 100; // 1 dakikalık load average'ı yüzde olarak
        }
        
        return 0;
    }

    protected function getActiveJobCount(): int
    {
        try {
            $redis = Redis::connection();
            $count = 0;
            
            foreach ($this->queueMappings as $priority => $queues) {
                foreach ($queues as $queue) {
                    $count += $redis->llen("queues:{$queue}");
                }
            }
            
            return $count;
        } catch (\Exception $e) {
            return 0;
        }
    }

    protected function getFailedJobCount(): int
    {
        try {
            return \DB::table('failed_jobs')->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    protected function getAllQueueSizes(): array
    {
        $sizes = [];
        
        try {
            $redis = Redis::connection();
            
            foreach ($this->queueMappings as $priority => $queues) {
                foreach ($queues as $queue) {
                    $sizes[$queue] = $redis->llen("queues:{$queue}");
                }
            }
        } catch (\Exception $e) {
            Log::warning('Failed to get queue sizes', ['error' => $e->getMessage()]);
        }
        
        return $sizes;
    }

    protected function executeEscalationAction(PrioritizableJobInterface $job, array $rule): void
    {
        $action = $rule['action'] ?? 'log';
        
        switch ($action) {
            case 'notify_admin':
                // Admin'e bildirim gönder
                break;
            case 'increase_workers':
                // Worker sayısını artır
                break;
            case 'move_to_priority_queue':
                // Priority queue'ya taşı
                break;
            default:
                // Sadece log
                break;
        }
    }
}
