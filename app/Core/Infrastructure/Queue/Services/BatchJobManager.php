<?php

namespace App\Core\Infrastructure\Queue\Services;

use App\Core\Infrastructure\Queue\Contracts\BatchProcessableInterface;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

/**
 * Batch Job Manager
 * Batch işleme yönetimi
 */
class BatchJobManager
{
    protected array $config;
    protected array $activeBatches = [];

    public function __construct()
    {
        $this->config = config('queue_infrastructure.batch_processing', []);
    }

    /**
     * Batch job oluştur ve dispatch et
     */
    public function createBatch(
        string $name,
        array $jobs,
        array $options = []
    ): Batch {
        $batchOptions = array_merge([
            'name' => $name,
            'allowFailures' => $options['allow_failures'] ?? false,
            'catch' => $options['catch_callback'] ?? null,
            'then' => $options['then_callback'] ?? null,
            'finally' => $options['finally_callback'] ?? null,
        ], $options);

        // Batch'i oluştur
        $batch = Bus::batch($jobs);

        // Options'ları uygula
        if (isset($batchOptions['name'])) {
            $batch->name($batchOptions['name']);
        }

        if ($batchOptions['allowFailures']) {
            $batch->allowFailures();
        }

        if ($batchOptions['catch']) {
            $batch->catch($batchOptions['catch']);
        }

        if ($batchOptions['then']) {
            $batch->then($batchOptions['then']);
        }

        if ($batchOptions['finally']) {
            $batch->finally($batchOptions['finally']);
        }

        // Batch'i dispatch et
        $dispatchedBatch = $batch->dispatch();

        // Batch tracking
        $this->trackBatch($dispatchedBatch, $options);

        Log::info('Batch job created and dispatched', [
            'batch_id' => $dispatchedBatch->id,
            'batch_name' => $name,
            'job_count' => count($jobs),
            'options' => $options,
        ]);

        return $dispatchedBatch;
    }

    /**
     * Chunked batch job oluştur
     */
    public function createChunkedBatch(
        string $name,
        Collection $data,
        string $jobClass,
        array $options = []
    ): Batch {
        $chunkSize = $options['chunk_size'] ?? $this->config['default_chunk_size'] ?? 100;
        $chunks = $data->chunk($chunkSize);
        
        $jobs = [];
        foreach ($chunks as $index => $chunk) {
            $jobs[] = new $jobClass($chunk->toArray(), $index, [
                'batch_name' => $name,
                'chunk_index' => $index,
                'total_chunks' => $chunks->count(),
            ]);
        }

        return $this->createBatch($name, $jobs, $options);
    }

    /**
     * Parallel batch job oluştur
     */
    public function createParallelBatch(
        string $name,
        array $jobs,
        array $options = []
    ): Batch {
        $maxParallel = $options['max_parallel'] ?? $this->config['max_parallel_jobs'] ?? 5;
        
        // Job'ları paralel gruplar halinde organize et
        $jobGroups = array_chunk($jobs, $maxParallel);
        $parallelJobs = [];

        foreach ($jobGroups as $groupIndex => $jobGroup) {
            foreach ($jobGroup as $jobIndex => $job) {
                // Her job'a paralel processing metadata'sı ekle
                if (method_exists($job, 'setParallelMetadata')) {
                    $job->setParallelMetadata([
                        'group_index' => $groupIndex,
                        'job_index' => $jobIndex,
                        'max_parallel' => $maxParallel,
                    ]);
                }
                
                $parallelJobs[] = $job;
            }
        }

        $options['parallel_processing'] = true;
        $options['max_parallel'] = $maxParallel;

        return $this->createBatch($name, $parallelJobs, $options);
    }

    /**
     * Conditional batch job oluştur
     */
    public function createConditionalBatch(
        string $name,
        array $jobs,
        callable $condition,
        array $options = []
    ): ?Batch {
        if (!$condition()) {
            Log::info('Conditional batch skipped', [
                'batch_name' => $name,
                'reason' => 'Condition not met',
            ]);
            return null;
        }

        return $this->createBatch($name, $jobs, $options);
    }

    /**
     * Scheduled batch job oluştur
     */
    public function createScheduledBatch(
        string $name,
        array $jobs,
        \DateTimeInterface $scheduledAt,
        array $options = []
    ): string {
        $batchData = [
            'name' => $name,
            'jobs' => serialize($jobs),
            'options' => $options,
            'scheduled_at' => $scheduledAt,
            'status' => 'scheduled',
            'created_at' => now(),
        ];

        $batchId = DB::table('scheduled_batches')->insertGetId($batchData);

        Log::info('Scheduled batch created', [
            'batch_id' => $batchId,
            'batch_name' => $name,
            'scheduled_at' => $scheduledAt->format('Y-m-d H:i:s'),
            'job_count' => count($jobs),
        ]);

        return $batchId;
    }

    /**
     * Batch progress'ini al
     */
    public function getBatchProgress(string $batchId): array
    {
        try {
            $batch = Bus::findBatch($batchId);
            
            if (!$batch) {
                return ['error' => 'Batch not found'];
            }

            $progress = [
                'id' => $batch->id,
                'name' => $batch->name,
                'total_jobs' => $batch->totalJobs,
                'pending_jobs' => $batch->pendingJobs,
                'processed_jobs' => $batch->processedJobs(),
                'failed_jobs' => $batch->failedJobs,
                'progress_percentage' => $batch->progress(),
                'finished' => $batch->finished(),
                'cancelled' => $batch->cancelled(),
                'created_at' => $batch->createdAt,
                'finished_at' => $batch->finishedAt,
            ];

            // Custom progress tracking varsa ekle
            $customProgress = $this->getCustomBatchProgress($batchId);
            if ($customProgress) {
                $progress['custom_metrics'] = $customProgress;
            }

            return $progress;

        } catch (\Exception $e) {
            Log::error('Failed to get batch progress', [
                'batch_id' => $batchId,
                'error' => $e->getMessage(),
            ]);

            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Batch'i iptal et
     */
    public function cancelBatch(string $batchId): bool
    {
        try {
            $batch = Bus::findBatch($batchId);
            
            if (!$batch) {
                return false;
            }

            $batch->cancel();

            Log::info('Batch cancelled', [
                'batch_id' => $batchId,
                'batch_name' => $batch->name,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to cancel batch', [
                'batch_id' => $batchId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Failed batch job'ları retry et
     */
    public function retryFailedBatchJobs(string $batchId): bool
    {
        try {
            $batch = Bus::findBatch($batchId);
            
            if (!$batch) {
                return false;
            }

            // Failed job'ları yeniden kuyruğa ekle
            foreach ($batch->failedJobIds as $failedJobId) {
                // Job'ı yeniden dispatch et
                $this->retryFailedJob($failedJobId, $batchId);
            }

            Log::info('Failed batch jobs retried', [
                'batch_id' => $batchId,
                'failed_job_count' => count($batch->failedJobIds),
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to retry batch jobs', [
                'batch_id' => $batchId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Batch istatistiklerini al
     */
    public function getBatchStatistics(?\DateTimeInterface $since = null): array
    {
        $since = $since ?? now()->subDays(7);

        try {
            $stats = DB::table('job_batches')
                ->where('created_at', '>=', $since)
                ->selectRaw('
                    COUNT(*) as total_batches,
                    SUM(total_jobs) as total_jobs,
                    SUM(failed_jobs) as total_failed_jobs,
                    AVG(total_jobs) as avg_jobs_per_batch,
                    COUNT(CASE WHEN finished_at IS NOT NULL THEN 1 END) as completed_batches,
                    COUNT(CASE WHEN cancelled_at IS NOT NULL THEN 1 END) as cancelled_batches
                ')
                ->first();

            return [
                'total_batches' => $stats->total_batches ?? 0,
                'total_jobs' => $stats->total_jobs ?? 0,
                'total_failed_jobs' => $stats->total_failed_jobs ?? 0,
                'avg_jobs_per_batch' => round($stats->avg_jobs_per_batch ?? 0, 2),
                'completed_batches' => $stats->completed_batches ?? 0,
                'cancelled_batches' => $stats->cancelled_batches ?? 0,
                'success_rate' => $stats->total_batches > 0 
                    ? round(($stats->completed_batches / $stats->total_batches) * 100, 2) 
                    : 0,
                'period' => [
                    'since' => $since->format('Y-m-d H:i:s'),
                    'until' => now()->format('Y-m-d H:i:s'),
                ],
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get batch statistics', [
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Aktif batch'leri al
     */
    public function getActiveBatches(): Collection
    {
        try {
            return DB::table('job_batches')
                ->whereNull('finished_at')
                ->whereNull('cancelled_at')
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($batch) {
                    return [
                        'id' => $batch->id,
                        'name' => $batch->name,
                        'total_jobs' => $batch->total_jobs,
                        'pending_jobs' => $batch->pending_jobs,
                        'failed_jobs' => $batch->failed_jobs,
                        'progress' => $batch->total_jobs > 0 
                            ? round((($batch->total_jobs - $batch->pending_jobs) / $batch->total_jobs) * 100, 2)
                            : 0,
                        'created_at' => $batch->created_at,
                    ];
                });

        } catch (\Exception $e) {
            Log::error('Failed to get active batches', [
                'error' => $e->getMessage(),
            ]);

            return collect();
        }
    }

    /**
     * Batch tracking
     */
    protected function trackBatch(Batch $batch, array $options): void
    {
        $this->activeBatches[$batch->id] = [
            'batch' => $batch,
            'options' => $options,
            'started_at' => now(),
        ];

        // Custom tracking varsa kaydet
        if ($options['track_progress'] ?? false) {
            $this->initializeCustomTracking($batch->id, $options);
        }
    }

    /**
     * Custom progress tracking başlat
     */
    protected function initializeCustomTracking(string $batchId, array $options): void
    {
        $trackingData = [
            'batch_id' => $batchId,
            'custom_metrics' => $options['custom_metrics'] ?? [],
            'started_at' => now(),
        ];

        Cache::put("batch_tracking:{$batchId}", $trackingData, 3600);
    }

    /**
     * Custom batch progress al
     */
    protected function getCustomBatchProgress(string $batchId): ?array
    {
        return Cache::get("batch_tracking:{$batchId}");
    }

    /**
     * Failed job'ı retry et
     */
    protected function retryFailedJob(string $failedJobId, string $batchId): void
    {
        // Implementation for retrying individual failed jobs
        // Bu method failed job'ı yeniden kuyruğa ekler
    }
}
