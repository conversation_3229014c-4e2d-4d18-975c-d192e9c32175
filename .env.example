APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=reverb
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

# Broadcasting & Real-Time Configuration
REVERB_APP_ID=local
REVERB_APP_KEY=local-key
REVERB_APP_SECRET=local-secret
REVERB_HOST=127.0.0.1
REVERB_PORT=8080
REVERB_SCHEME=http

# Pusher Configuration (alternative)
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

# Real-Time Features
REALTIME_ENABLED=true
REALTIME_DEBUG=false
REALTIME_PRODUCT_STOCK=true
REALTIME_PRODUCT_PRICE=true
REALTIME_ORDER_STATUS=true
REALTIME_CART_UPDATES=true
REALTIME_NOTIFICATIONS=true
REALTIME_ADMIN_ALERTS=true

# Real-Time Performance
REALTIME_MAX_CONNECTIONS=5
REALTIME_CONNECTION_TIMEOUT=60
REALTIME_HEARTBEAT_INTERVAL=30
REALTIME_MAX_MESSAGE_SIZE=1024

# Real-Time Rate Limiting
REALTIME_RATE_LIMITING=true
REALTIME_MAX_EVENTS_PER_MINUTE=60
REALTIME_MAX_EVENTS_PER_HOUR=1000
REALTIME_MAX_CONNECTIONS_PER_IP=10

# Real-Time Caching
REALTIME_CACHE_ENABLED=true
REALTIME_CACHE_DRIVER=redis
REALTIME_CACHE_PREFIX=realtime
REALTIME_CACHE_TTL=300

# Real-Time Monitoring
REALTIME_MONITORING=true
REALTIME_LOG_CONNECTIONS=false
REALTIME_LOG_EVENTS=false
REALTIME_METRICS=true

# Real-Time Security
REALTIME_AUTH_ENABLED=true
REALTIME_CSRF_PROTECTION=true
REALTIME_ORIGIN_CHECK=true
REALTIME_ALLOWED_ORIGINS=*

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Elasticsearch Configuration
ELASTICSEARCH_HOST=localhost:9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
ELASTICSEARCH_CLOUD_ID=
ELASTICSEARCH_API_KEY=
ELASTICSEARCH_SSL_VERIFICATION=true
ELASTICSEARCH_RETRIES=2
ELASTICSEARCH_TIMEOUT=30

# Elasticsearch Index Settings
ELASTICSEARCH_PRODUCTS_INDEX=products
ELASTICSEARCH_PRODUCTS_SHARDS=1
ELASTICSEARCH_PRODUCTS_REPLICAS=1
ELASTICSEARCH_ANALYTICS_INDEX=analytics
ELASTICSEARCH_ANALYTICS_SHARDS=1
ELASTICSEARCH_ANALYTICS_REPLICAS=0

# Elasticsearch Search Settings
ELASTICSEARCH_DEFAULT_SIZE=20
ELASTICSEARCH_MAX_SIZE=1000
ELASTICSEARCH_SEARCH_TIMEOUT=30s
ELASTICSEARCH_TRACK_TOTAL_HITS=true
ELASTICSEARCH_HIGHLIGHT_ENABLED=true

# Elasticsearch Analytics Settings
ELASTICSEARCH_ANALYTICS_ENABLED=true
ELASTICSEARCH_ANALYTICS_BATCH_SIZE=100
ELASTICSEARCH_ANALYTICS_FLUSH_INTERVAL=30
ELASTICSEARCH_ANALYTICS_RETENTION_DAYS=90
ELASTICSEARCH_ANALYTICS_REAL_TIME=true

# Elasticsearch Performance Settings
ELASTICSEARCH_CACHE_ENABLED=true
ELASTICSEARCH_CACHE_TTL=300
ELASTICSEARCH_BULK_SIZE=1000
ELASTICSEARCH_SCROLL_SIZE=1000
ELASTICSEARCH_SCROLL_TIMEOUT=5m

# Elasticsearch Logging
ELASTICSEARCH_LOGGING_ENABLED=false
ELASTICSEARCH_LOG_LEVEL=info
ELASTICSEARCH_LOG_CHANNEL=elasticsearch

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# API Versioning Configuration
API_VERSION_STRATEGY=header
API_DEFAULT_VERSION=1.0
API_CURRENT_VERSION=1.0
API_LATEST_VERSION=1.1

# API Features
API_FEATURE_VERSIONING=true
API_FEATURE_RATE_LIMITING=true
API_FEATURE_CACHING=true
API_FEATURE_DOCUMENTATION=true
API_FEATURE_MONITORING=false
API_FEATURE_ANALYTICS=false

# API Versioning Behavior
API_BACKWARD_COMPATIBILITY=true
API_AUTO_UPGRADE=false
API_STRICT_VERSION=false
API_FALLBACK_LATEST=false

# API Deprecation
API_DEPRECATION_WARNING_DAYS=90
API_SUNSET_PERIOD_DAYS=180
API_INCLUDE_DEPRECATION_WARNINGS=true
API_LOG_DEPRECATED_USAGE=true

# API Version Caching
API_VERSION_CACHE=true
API_VERSION_CACHE_TTL=3600

# API Route Discovery
API_ROUTE_AUTO_DISCOVERY=true
API_CONTROLLER_VERSIONING=true
API_RESPONSE_VERSIONING=true

# API Documentation
API_DOCS_ENABLED=true
API_DOCS_AUTO_GENERATE=false
API_DOCS_FORMAT=openapi
API_DOCS_TITLE="ModularEcommerce API"
API_DOCS_DESCRIPTION="ModularEcommerce API Documentation"
API_DOCS_VERSION=1.0.0
API_DOCS_CONTACT_NAME="API Support"
API_DOCS_CONTACT_EMAIL="<EMAIL>"
API_DOCS_PER_VERSION=true
API_DOCS_INCLUDE_DEPRECATED=true
API_DOCS_VERSION_COMPARISON=true
API_DOCS_MIGRATION_GUIDES=true

# API Error Handling
API_DETAILED_VERSION_ERRORS=true

# API Monitoring
API_VERSION_MONITORING=true
API_TRACK_VERSION_USAGE=true
API_VERSION_METRICS=false
API_VERSION_ANALYTICS=false

# API Validation Settings
API_VALIDATION_STRICT=false
API_VALIDATION_STOP_ON_FIRST=false
API_VALIDATION_INCLUDE_FIELDS=true
API_VALIDATION_INCLUDE_VALUES=false
API_VALIDATION_AUTO_VALIDATE=true
API_VALIDATION_CACHE_RULES=true
API_VALIDATION_CUSTOM_MESSAGES=true
API_VALIDATION_LOCALIZATION=true
API_VALIDATION_DEFAULT_LOCALE=tr
API_VALIDATION_FALLBACK_LOCALE=en
API_VALIDATION_INCLUDE_LABELS=true
API_VALIDATION_MESSAGE_FORMAT=structured
API_VALIDATION_CUSTOM_RULES=true
API_VALIDATION_AUTO_REGISTER_RULES=true
API_VALIDATION_MIDDLEWARE=true
API_VALIDATION_AUTO_APPLY=true
API_VALIDATION_ERROR_FORMAT=structured
API_VALIDATION_INCLUDE_META=true
API_VALIDATION_INCLUDE_SUGGESTIONS=true
API_VALIDATION_GROUP_BY_FIELD=true
API_VALIDATION_CACHE=true
API_VALIDATION_CACHE_TTL=3600
API_VALIDATION_LAZY_LOADING=true
API_VALIDATION_RULE_COMPILATION=true
API_VALIDATION_SANITIZE=true
API_VALIDATION_MAX_INPUT_SIZE=10M
API_VALIDATION_MAX_ARRAY_DEPTH=10
API_VALIDATION_MAX_ARRAY_ITEMS=1000
API_VALIDATION_LOGGING=false
API_VALIDATION_LOG_CHANNEL=api
API_VALIDATION_LOG_LEVEL=info
API_VALIDATION_LOG_FAILURES=true
API_VALIDATION_LOG_PERFORMANCE=false

# API Rate Limiting & Security Settings
API_RATE_LIMITING_ENABLED=true
API_RATE_LIMITING_DRIVER=redis
API_RATE_LIMITING_KEY_GENERATOR=ip
API_RATE_LIMITING_RESPONSE_HEADERS=true
API_RATE_LIMITING_SKIP_SUCCESSFUL=false
API_RATE_LIMITING_SKIP_FAILED=false
API_RATE_LIMIT_GLOBAL_REQUESTS=1000
API_RATE_LIMIT_GLOBAL_PER_MINUTE=60
API_RATE_LIMIT_GLOBAL_BURST=100
API_RATE_LIMIT_AUTH_REQUESTS=500
API_RATE_LIMIT_AUTH_PER_MINUTE=60
API_RATE_LIMIT_AUTH_BURST=50
API_RATE_LIMIT_GUEST_REQUESTS=100
API_RATE_LIMIT_GUEST_PER_MINUTE=60
API_RATE_LIMIT_GUEST_BURST=20
API_RATE_LIMIT_API_KEY_REQUESTS=2000
API_RATE_LIMIT_API_KEY_PER_MINUTE=60
API_RATE_LIMIT_API_KEY_BURST=200
API_THROTTLE_ENABLED=true
API_THROTTLE_ALGORITHM=sliding_window
API_THROTTLE_WINDOW_SIZE=60
API_THROTTLE_MAX_ATTEMPTS=5
API_THROTTLE_DECAY_MINUTES=1
API_THROTTLE_PREFIX=api_throttle
API_RATE_LIMITING_BYPASS_ENABLED=true
API_RATE_LIMITING_BYPASS_IPS=
API_RATE_LIMITING_BYPASS_USER_AGENTS=
API_RATE_LIMITING_BYPASS_API_KEYS=
API_SECURITY_ENABLED=true
API_SECURITY_STRICT_MODE=false
API_SECURITY_BLOCK_SUSPICIOUS=true
API_SECURITY_LOG_EVENTS=true
API_SECURITY_ALERT_ATTACKS=false
API_IP_FILTERING_ENABLED=false
API_IP_FILTERING_MODE=blacklist
API_IP_WHITELIST=
API_IP_BLACKLIST=
API_IP_AUTO_BLACKLIST=true
API_IP_AUTO_BLACKLIST_THRESHOLD=100
API_IP_AUTO_BLACKLIST_DURATION=3600
API_KEYS_ENABLED=true
API_KEYS_REQUIRED_ROUTES=
API_KEY_HEADER_NAME=X-API-Key
API_KEY_QUERY_PARAM=api_key
API_KEY_ALLOW_QUERY_PARAM=false
API_KEY_LENGTH=32
API_KEY_PREFIX=ak_
API_KEY_EXPIRATION_DAYS=365
API_KEY_AUTO_ROTATE=false
API_KEY_AUTO_ROTATE_DAYS=90
API_CORS_ENABLED=true
API_CORS_ALLOWED_ORIGINS=*
API_CORS_ALLOWED_METHODS=GET,POST,PUT,PATCH,DELETE,OPTIONS
API_CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-API-Key,X-API-Version
API_CORS_EXPOSED_HEADERS=X-RateLimit-Limit,X-RateLimit-Remaining
API_CORS_MAX_AGE=86400
API_CORS_SUPPORTS_CREDENTIALS=false
API_SECURITY_HEADERS_ENABLED=true
API_POWERED_BY=ModularEcommerce
API_RATE_LIMITING_MONITORING=true
API_RATE_LIMITING_LOG_CHANNEL=api
API_RATE_LIMITING_LOG_LEVEL=info
API_RATE_LIMITING_LOG_BLOCKED=true
API_RATE_LIMITING_LOG_WARNINGS=true
API_RATE_LIMITING_METRICS=false
API_RATE_LIMITING_ALERT_THRESHOLD=80
API_RATE_LIMITING_CACHE_STORE=redis
API_RATE_LIMITING_CACHE_PREFIX=api_rate_limit
API_RATE_LIMITING_CACHE_TTL=3600
API_RATE_LIMITING_CACHE_CLEANUP=true
API_RATE_LIMITING_CACHE_CLEANUP_INTERVAL=300
API_RATE_LIMITING_STATUS_CODE=429
API_RATE_LIMITING_MESSAGE=Too Many Requests
API_RATE_LIMITING_INCLUDE_RETRY_AFTER=true
API_RATE_LIMITING_INCLUDE_HEADERS=true
API_RATE_LIMITING_CUSTOM_RESPONSE=false
